import { Redis } from '@upstash/redis';
import logger from '../config/logger.js';

// Initialize Redis client
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL,
  token: process.env.UPSTASH_REDIS_REST_TOKEN,
});

// Cache configuration
const CACHE_CONFIG = {
  defaultTTL: 300, // 5 minutes
  shortTTL: 60,    // 1 minute
  mediumTTL: 900,  // 15 minutes
  longTTL: 3600,   // 1 hour
  veryLongTTL: 86400, // 24 hours
};

class CacheManager {
  constructor() {
    this.redis = redis;
    this.prefix = process.env.CACHE_PREFIX || 'wallet:';
  }

  // Generate cache key with prefix
  generateKey(key) {
    return `${this.prefix}${key}`;
  }

  // Set cache with TTL
  async set(key, value, ttl = CACHE_CONFIG.defaultTTL) {
    try {
      const cacheKey = this.generateKey(key);
      const serializedValue = JSON.stringify({
        data: value,
        timestamp: Date.now(),
        ttl: ttl
      });
      
      await this.redis.setex(cacheKey, ttl, serializedValue);
      logger.debug(`Cache set: ${cacheKey} (TTL: ${ttl}s)`);
      return true;
    } catch (error) {
      logger.error('Cache set error:', error);
      return false;
    }
  }

  // Get cache
  async get(key) {
    try {
      const cacheKey = this.generateKey(key);
      const cached = await this.redis.get(cacheKey);
      
      if (!cached) {
        logger.debug(`Cache miss: ${cacheKey}`);
        return null;
      }

      const parsed = JSON.parse(cached);
      logger.debug(`Cache hit: ${cacheKey}`);
      return parsed.data;
    } catch (error) {
      logger.error('Cache get error:', error);
      return null;
    }
  }

  // Delete cache
  async del(key) {
    try {
      const cacheKey = this.generateKey(key);
      await this.redis.del(cacheKey);
      logger.debug(`Cache deleted: ${cacheKey}`);
      return true;
    } catch (error) {
      logger.error('Cache delete error:', error);
      return false;
    }
  }

  // Delete multiple keys by pattern
  async delPattern(pattern) {
    try {
      const keys = await this.redis.keys(`${this.prefix}${pattern}`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        logger.debug(`Cache pattern deleted: ${pattern} (${keys.length} keys)`);
      }
      return true;
    } catch (error) {
      logger.error('Cache pattern delete error:', error);
      return false;
    }
  }

  // Increment counter
  async incr(key, ttl = CACHE_CONFIG.defaultTTL) {
    try {
      const cacheKey = this.generateKey(key);
      const result = await this.redis.incr(cacheKey);
      
      // Set TTL only for new keys
      if (result === 1) {
        await this.redis.expire(cacheKey, ttl);
      }
      
      return result;
    } catch (error) {
      logger.error('Cache increment error:', error);
      return 0;
    }
  }

  // Check if key exists
  async exists(key) {
    try {
      const cacheKey = this.generateKey(key);
      return await this.redis.exists(cacheKey);
    } catch (error) {
      logger.error('Cache exists error:', error);
      return false;
    }
  }

  // Get cache statistics
  async getStats() {
    try {
      const info = await this.redis.info();
      return {
        connected: true,
        info: info
      };
    } catch (error) {
      logger.error('Cache stats error:', error);
      return {
        connected: false,
        error: error.message
      };
    }
  }
}

// Create cache manager instance
const cacheManager = new CacheManager();

// Cache middleware factory
const cache = (options = {}) => {
  const {
    ttl = CACHE_CONFIG.defaultTTL,
    keyGenerator = null,
    condition = null,
    skipCache = false
  } = options;

  return async (req, res, next) => {
    // Skip cache if disabled or condition not met
    if (skipCache || (condition && !condition(req))) {
      return next();
    }

    // Generate cache key
    let cacheKey;
    if (keyGenerator && typeof keyGenerator === 'function') {
      cacheKey = keyGenerator(req);
    } else {
      // Default key generation
      const method = req.method;
      const url = req.originalUrl;
      const userId = req.user?.id || 'anonymous';
      cacheKey = `${method}:${url}:${userId}`;
    }

    try {
      // Try to get from cache
      const cached = await cacheManager.get(cacheKey);
      
      if (cached) {
        logger.debug(`Cache hit for: ${cacheKey}`);
        return res.json(cached);
      }

      // Cache miss - continue to route handler
      logger.debug(`Cache miss for: ${cacheKey}`);

      // Override res.json to cache the response
      const originalJson = res.json;
      res.json = function(data) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          cacheManager.set(cacheKey, data, ttl).catch(err => {
            logger.error('Failed to cache response:', err);
          });
        }
        
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next();
    }
  };
};

// Specific cache middlewares
const cacheProducts = cache({
  ttl: CACHE_CONFIG.mediumTTL,
  keyGenerator: (req) => {
    const { category, featured, limit, page } = req.query;
    return `products:${category || 'all'}:${featured || 'all'}:${limit || 'all'}:${page || 1}`;
  }
});

const cacheProduct = cache({
  ttl: CACHE_CONFIG.longTTL,
  keyGenerator: (req) => `product:${req.params.id}`
});

const cacheCategories = cache({
  ttl: CACHE_CONFIG.veryLongTTL,
  keyGenerator: () => 'categories:all'
});

const cacheUserTransactions = cache({
  ttl: CACHE_CONFIG.shortTTL,
  keyGenerator: (req) => `transactions:user:${req.params.userId}:${req.query.page || 1}`
});

// Cache invalidation helpers
const invalidateProductCache = async (productId = null) => {
  if (productId) {
    await cacheManager.del(`product:${productId}`);
  }
  await cacheManager.delPattern('products:*');
  logger.info('Product cache invalidated');
};

const invalidateCategoryCache = async () => {
  await cacheManager.delPattern('categories:*');
  logger.info('Category cache invalidated');
};

const invalidateUserCache = async (userId) => {
  await cacheManager.delPattern(`*:user:${userId}:*`);
  logger.info(`User cache invalidated for user: ${userId}`);
};

// Rate limiting with cache
const rateLimitCache = async (identifier, limit, window) => {
  const key = `ratelimit:${identifier}`;
  const current = await cacheManager.incr(key, window);
  
  return {
    count: current,
    remaining: Math.max(0, limit - current),
    resetTime: Date.now() + (window * 1000),
    exceeded: current > limit
  };
};

export {
  cacheManager,
  cache,
  cacheProducts,
  cacheProduct,
  cacheCategories,
  cacheUserTransactions,
  invalidateProductCache,
  invalidateCategoryCache,
  invalidateUserCache,
  rateLimitCache,
  CACHE_CONFIG
};
