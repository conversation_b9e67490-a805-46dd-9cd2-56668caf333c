import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import {sql} from './config/db.js';
import rateLimiter from './middleware/rateLimiter.js';


dotenv.config();

const app = express();


// middleware req ve server arasındaki bağlantı

app.use(cors());
app.use(express.json());
app.use(rateLimiter);

// Categories table
async function initCategoriesTable() {
    try {
        await sql`CREATE TABLE IF NOT EXISTS categories(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`;
        console.log('Categories table initialized');
    } catch (error) {
        console.log('Error initializing categories table:', error);
    }
}


// our custom middleware
// app.use((req,res,next) =>  {
//     console.log('Request received',req.method);
//     next();
// });

const PORT = process.env.PORT || 5001;

async function initDB() {
    try {
        await sql`CREATE TABLE IF NOT EXISTS transactions(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            title VARCHAR(255) NOT NULL,
            amount DECIMAL NOT NULL,
            category VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;

        await sql`CREATE TABLE IF NOT EXISTS categories(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`;

        await sql`CREATE TABLE IF NOT EXISTS products(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            price DECIMAL NOT NULL,
            original_price DECIMAL NOT NULL,
            discount_percentage DECIMAL NOT NULL,
            images JSONB NOT NULL,
            category VARCHAR(255) NOT NULL,
            stock INTEGER NOT NULL,
            featured BOOLEAN NOT NULL,
            status VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            specifications JSONB NOT NULL,
            tags JSONB NOT NULL,
            dynamic_pricing JSONB NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;

        console.log('Database initialized successfully');
    } catch (error) {
        console.log('Error initializing DB', error);
        process.exit(1);//status code 1 means falure 0 means success
    }
}


app.get("/",(req,res) => {
    res.send('Hello World');
});

// Categories API endpoints
app.get('/api/categories', async (req, res) => {
    try {
        const categories = await sql`SELECT * FROM categories ORDER BY created_at DESC`;
        res.status(200).json(categories);
    } catch (error) {
        console.error('Error fetching categories:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

app.post('/api/categories', async (req, res) => {
    try {
        const { name, description } = req.body;

        if (!name) {
            return res.status(400).json({ message: 'Category name is required' });
        }

        const category = await sql`
            INSERT INTO categories (name, description)
            VALUES (${name}, ${description || ''})
            RETURNING *
        `;

        res.status(201).json(category[0]);
    } catch (error) {
        console.error('Error creating category:', error);
        if (error.message.includes('duplicate key')) {
            res.status(409).json({ message: 'Category already exists' });
        } else {
            res.status(500).json({ message: "Internal server error" });
        }
    }
});

app.put('/api/categories/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description } = req.body;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }

        const result = await sql`
            UPDATE categories
            SET name = ${name}, description = ${description}, updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
            RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Category not found" });
        }

        res.status(200).json(result[0]);
    } catch (error) {
        console.error('Error updating category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

app.delete('/api/categories/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid category ID" });
        }

        const result = await sql`
            DELETE FROM categories WHERE id = ${id} RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Category not found" });
        }

        res.status(200).json({ message: "Category deleted successfully" });
    } catch (error) {
        console.error('Error deleting category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});
app.get('/api/transactions/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const transactions = await sql`SELECT * FROM transactions WHERE user_id = ${userId} ORDER BY created_at DESC`;
        res.status(200).json(transactions);
    } catch (error) {
        console.log('Error getting transactions', error);
        res.status(500).json({message:"internal server error"});
    }
});
app.post('/api/transactions', async (req, res) => {
    try {
        const {user_id, title, amount, category} = req.body;

        if(!user_id || !title || !amount || !category) {
         return res.status(400).send('Missing required fields');
        }

        const transaction = await sql`
        INSERT INTO transactions(user_id, title, amount, category) 
        VALUES (${user_id}, ${title}, ${amount}, ${category})
        RETURNING*`;

        console.log();
        res.status(201).json(transaction[0]);

    } catch (error) {
        console.log('Error creating transaction', error);
        res.status(500).json("internal server error");
    }
});
app.delete('/api/transactions/:id', async (req, res) => {
    try {
        const {id} = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({message:"Invalid transaction ID"});
        }
        const result = await sql`
        DELETE FROM transactions WHERE id = ${id} RETURNING *
        `
        if (result.length === 0 ){
            return res.status(404).json({message:"Transaction not found"});
        };
        res.status(200).json({message:"Transaction deleted successfully"});
    } catch (error) {
        console.log('Error deleting transaction', error);
        res.status(500).json({message:"internal server error"});
    }
});
app.put('/api/transactions/:id', async (req, res) => {
    try {
        const {id} = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({message:"Invalid transaction ID"});
        }
        const {title, amount, category} = req.body;

        const result = await sql`
        UPDATE transactions SET title = ${title}, amount = ${amount}, category = ${category} WHERE id = ${id} RETURNING *
        `
        if (result.length === 0) {
            return res.status(404).json({message:"Transaction not found"});
        }
        res.status(200).json(result[0]);
    } catch (error) {
        console.log('Error updating transaction', error);
        res.status(500).json({message:"internal server error"});
    }   
});
app.get('/api/transactions/summary/:userId', async (req, res) => {
    try {
        const {userId} = req.params;

        const balanceResult = await sql`
        SELECT COALESCE(SUM(amount), 0) as balance
        FROM transactions
        WHERE user_id = ${userId}
        `
        const incomeResult = await sql`
        SELECT COALESCE(SUM(amount), 0) as income
        FROM transactions
        WHERE user_id = ${userId} AND amount > 0
        `

        const expenseResult = await sql`
        SELECT COALESCE(SUM(amount), 0) as expense
        FROM transactions
        WHERE user_id = ${userId} AND amount < 0
        `

        res.status(200).json({
            balance: balanceResult[0].balance,
            income: incomeResult[0].income,
            expense: expenseResult[0].expense
        });
    } catch (error) {
        console.log('Error getting summary', error);
        res.status(500).json({message:"internal server error"});
    }
});

// Product API endpoints
app.get('/api/products', async (req, res) => {
    try {
        const products = await sql`
            SELECT * FROM products
            WHERE status = 'active'
            ORDER BY created_at DESC
        `;
        res.status(200).json(products);
    } catch (error) {
        console.error('Error fetching products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

app.get('/api/products/user/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const products = await sql`
            SELECT * FROM products
            WHERE seller_id = ${userId}
            ORDER BY created_at DESC
        `;
        res.status(200).json(products);
    } catch (error) {
        console.error('Error fetching user products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

app.get('/api/products/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid product ID" });
        }

        const product = await sql`
            SELECT * FROM products WHERE id = ${id}
        `;

        if (product.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json(product[0]);
    } catch (error) {
        console.error('Error fetching product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

app.post('/api/products', async (req, res) => {
    try {
        const {
            name,
            description,
            price,
            original_price,
            discount_percentage = 0,
            images = [],
            category,
            stock = 0,
            featured = false,
            status = 'active',
            seller_id,
            specifications = {},
            tags = [],
            dynamic_pricing = {}
        } = req.body;

        if (!name || !description || !price || !category || !seller_id) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        const product = await sql`
            INSERT INTO products (
                name, description, price, original_price, discount_percentage,
                images, category, stock, featured, status, seller_id,
                specifications, tags, dynamic_pricing
            ) VALUES (
                ${name}, ${description}, ${price}, ${original_price || price}, ${discount_percentage},
                ${JSON.stringify(images)}, ${category}, ${stock}, ${featured}, ${status}, ${seller_id},
                ${JSON.stringify(specifications)}, ${JSON.stringify(tags)}, ${JSON.stringify(dynamic_pricing)}
            ) RETURNING *
        `;

        res.status(201).json(product[0]);
    } catch (error) {
        console.error('Error creating product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

app.delete('/api/products/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid product ID" });
        }

        const result = await sql`
            DELETE FROM products WHERE id = ${id} RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json({ message: "Product deleted successfully" });
    } catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

console.log('Server is up and runnig on port:', PORT);

initDB().then(() => {
    app.listen(PORT, () => {
        console.log('Server is up and runnig on port:', PORT);
    });
});
