// Migration: Create products table with indexes and full-text search
// Created: 2024-12-01T00:00:02.000Z

export const up = async (sql) => {
  // Create products table
  await sql`
    CREATE TABLE IF NOT EXISTS products (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
      original_price DECIMAL(10,2) CHECK (original_price >= 0),
      discount_percentage INTEGER DEFAULT 0 CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
      images JSONB DEFAULT '[]',
      category VARCHAR(100) NOT NULL,
      stock INTEGER DEFAULT 0 CHECK (stock >= 0),
      featured BOOLEAN DEFAULT FALSE,
      status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
      seller_id VARCHAR(255) NOT NULL,
      specifications JSONB DEFAULT '{}',
      tags TEXT[],
      dynamic_pricing JSONB DEFAULT '{}',
      views_count INTEGER DEFAULT 0,
      sales_count INTEGER DEFAULT 0,
      rating DECIMAL(3,2) DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
      review_count INTEGER DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;

  // Create indexes for performance
  await sql`CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_status ON products(status)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_featured ON products(featured)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_seller_id ON products(seller_id)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_price ON products(price)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_rating ON products(rating)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_sales_count ON products(sales_count)`;

  // Create full-text search index
  await sql`
    CREATE INDEX IF NOT EXISTS idx_products_search 
    ON products USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')))
  `;

  // Create composite indexes for common queries
  await sql`CREATE INDEX IF NOT EXISTS idx_products_category_status ON products(category, status)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_products_featured_status ON products(featured, status) WHERE featured = true`;

  // Create trigger for updated_at
  await sql`
    CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
  `;
};

export const down = async (sql) => {
  await sql`DROP TRIGGER IF EXISTS update_products_updated_at ON products`;
  await sql`DROP TABLE IF EXISTS products CASCADE`;
};
