import request from 'supertest';
import express from 'express';
import productRoutes from '../../src/routes/productRoutes.js';
import { globalErrorHandler } from '../../src/middleware/errorHandler.js';

// Mock database
jest.mock('../../src/config/db.js', () => ({
  sql: jest.fn()
}));

describe('Product Routes Integration Tests', () => {
  let app;
  let mockSql;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    app.use('/api/products', productRoutes);
    app.use(globalErrorHandler);
  });

  beforeEach(async () => {
    const { sql } = await import('../../src/config/db.js');
    mockSql = sql;
    jest.clearAllMocks();
  });

  describe('GET /api/products', () => {
    it('should return all active products', async () => {
      const mockProducts = [
        {
          id: 1,
          name: 'Test Product 1',
          description: 'Test Description 1',
          price: 99.99,
          status: 'active'
        },
        {
          id: 2,
          name: 'Test Product 2',
          description: 'Test Description 2',
          price: 149.99,
          status: 'active'
        }
      ];

      mockSql.mockResolvedValue(mockProducts);

      const response = await request(app)
        .get('/api/products')
        .expect(200);

      expect(response.body).toEqual(mockProducts);
      expect(mockSql).toHaveBeenCalled();
    });

    it('should handle database errors', async () => {
      mockSql.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/products')
        .expect(500);

      expect(response.body).toHaveProperty('status', 'error');
    });
  });

  describe('GET /api/products/:id', () => {
    it('should return a specific product', async () => {
      const mockProduct = {
        id: 1,
        name: 'Test Product',
        description: 'Test Description',
        price: 99.99,
        status: 'active'
      };

      mockSql.mockResolvedValue([mockProduct]);

      const response = await request(app)
        .get('/api/products/1')
        .expect(200);

      expect(response.body).toEqual(mockProduct);
    });

    it('should return 404 for non-existent product', async () => {
      mockSql.mockResolvedValue([]);

      const response = await request(app)
        .get('/api/products/999')
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Product not found');
    });

    it('should return 400 for invalid product ID', async () => {
      const response = await request(app)
        .get('/api/products/invalid-id')
        .expect(400);

      expect(response.body).toHaveProperty('message', 'Invalid product ID');
    });
  });

  describe('POST /api/products', () => {
    it('should create a new product with valid data', async () => {
      const newProduct = {
        name: 'New Product',
        description: 'This is a new product description',
        price: 199.99,
        category: 'electronics',
        seller_id: 'seller123'
      };

      const createdProduct = {
        id: 1,
        ...newProduct,
        original_price: 199.99,
        discount_percentage: 0,
        images: [],
        stock: 0,
        featured: false,
        status: 'active',
        specifications: {},
        tags: [],
        dynamic_pricing: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      mockSql.mockResolvedValue([createdProduct]);

      const response = await request(app)
        .post('/api/products')
        .send(newProduct)
        .expect(201);

      expect(response.body).toEqual(createdProduct);
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteProduct = {
        name: 'Incomplete Product'
        // missing description, price, category, seller_id
      };

      const response = await request(app)
        .post('/api/products')
        .send(incompleteProduct)
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('required');
    });

    it('should handle database errors during creation', async () => {
      const newProduct = {
        name: 'New Product',
        description: 'This is a new product description',
        price: 199.99,
        category: 'electronics',
        seller_id: 'seller123'
      };

      mockSql.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .post('/api/products')
        .send(newProduct)
        .expect(500);

      expect(response.body).toHaveProperty('message', 'Internal server error');
    });
  });

  describe('PUT /api/products/:id', () => {
    it('should update an existing product', async () => {
      const updateData = {
        name: 'Updated Product',
        description: 'Updated description',
        price: 299.99
      };

      const updatedProduct = {
        id: 1,
        ...updateData,
        category: 'electronics',
        seller_id: 'seller123',
        updated_at: new Date().toISOString()
      };

      mockSql.mockResolvedValue([updatedProduct]);

      const response = await request(app)
        .put('/api/products/1')
        .send(updateData)
        .expect(200);

      expect(response.body).toEqual(updatedProduct);
    });

    it('should return 404 for non-existent product', async () => {
      const updateData = {
        name: 'Updated Product'
      };

      mockSql.mockResolvedValue([]);

      const response = await request(app)
        .put('/api/products/999')
        .send(updateData)
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Product not found');
    });
  });

  describe('DELETE /api/products/:id', () => {
    it('should delete an existing product', async () => {
      const deletedProduct = {
        id: 1,
        name: 'Deleted Product'
      };

      mockSql.mockResolvedValue([deletedProduct]);

      const response = await request(app)
        .delete('/api/products/1')
        .expect(200);

      expect(response.body).toHaveProperty('message', 'Product deleted successfully');
    });

    it('should return 404 for non-existent product', async () => {
      mockSql.mockResolvedValue([]);

      const response = await request(app)
        .delete('/api/products/999')
        .expect(404);

      expect(response.body).toHaveProperty('message', 'Product not found');
    });
  });
});
