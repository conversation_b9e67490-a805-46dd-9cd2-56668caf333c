name: Mobile CI/CD

on:
  push:
    branches: [main, develop]
    paths: ["mobile/**"]
  pull_request:
    branches: [main]
    paths: ["mobile/**"]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          cache-dependency-path: mobile/package-lock.json

      - name: Install dependencies
        run: |
          cd mobile
          npm ci

      - name: Create test environment file
        run: |
          cd mobile
          cat > .env.test << EOF
          EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_fake_key_for_testing
          EXPO_PUBLIC_API_URL=http://localhost:5001/api
          EXPO_PUBLIC_NODE_ENV=test
          EOF

      - name: Run linting
        run: |
          cd mobile
          npm run lint

      - name: Run type checking
        run: |
          cd mobile
          npx tsc --noEmit

      - name: Run unit tests
        run: |
          cd mobile
          npm test -- --coverage --watchAll=false

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./mobile/coverage/lcov.info
          flags: mobile
          name: mobile-coverage

  build-ios:
    runs-on: macos-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
          cache-dependency-path: mobile/package-lock.json

      - name: Setup Expo CLI
        run: npm install -g @expo/cli

      - name: Install dependencies
        run: |
          cd mobile
          npm ci

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Build iOS app
        run: |
          cd mobile
          expo build:ios --non-interactive

  build-android:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
          cache-dependency-path: mobile/package-lock.json

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Setup Android SDK
        uses: android-actions/setup-android@v2

      - name: Setup Expo CLI
        run: npm install -g @expo/cli

      - name: Install dependencies
        run: |
          cd mobile
          npm ci

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Build Android app
        run: |
          cd mobile
          expo build:android --non-interactive

  e2e-test:
    runs-on: macos-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"
          cache: "npm"
          cache-dependency-path: mobile/package-lock.json

      - name: Install dependencies
        run: |
          cd mobile
          npm ci

      - name: Setup iOS Simulator
        run: |
          xcrun simctl create "iPhone 14" "iPhone 14" "iOS16.0"
          xcrun simctl boot "iPhone 14"

      - name: Build iOS app for testing
        run: |
          cd mobile
          npx detox build --configuration ios.sim.debug

      - name: Run E2E tests
        run: |
          cd mobile
          npx detox test --configuration ios.sim.debug --cleanup

  deploy:
    needs: [test, build-ios, build-android]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Publish to Expo
        run: |
          cd mobile
          expo publish --non-interactive
