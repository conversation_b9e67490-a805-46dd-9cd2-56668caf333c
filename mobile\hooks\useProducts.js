export const API_URL = process.env.EXPO_PUBLIC_API_URL


export async function getProducts() {
    try {
        console.log(`Fetching products from: ${API_URL}/products`);
        const res = await fetch(`${API_URL}/products`);

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const data = await res.json();
        console.log('Products fetched successfully:', data);
        return data;
    } catch (error) {
        console.error('Error fetching products:', error);
        return [];
    }
}