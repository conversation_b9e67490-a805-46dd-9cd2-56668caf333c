import { useQuery } from '@tanstack/react-query';

export const API_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:5001/api';

console.log('API_URL configured as:', API_URL);

// API fetch function
export async function fetchProducts() {
    try {
        const url = `${API_URL}/products`;
        console.log(`Fetching products from: ${url}`);

        const res = await fetch(url);
        console.log('Response status:', res.status);

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const data = await res.json();
        console.log('Products fetched successfully. Count:', data.length);
        return data;
    } catch (error) {
        console.error('Error fetching products:', error);
        throw error;
    }
}

// TanStack Query hook
export function useProducts() {
    return useQuery({
        queryKey: ['products'],
        queryFn: fetchProducts,
        staleTime: 5 * 60 * 1000, // 5 dakika
        gcTime: 10 * 60 * 1000, // 10 dakika
    });
}

// Backward compatibility için eski function'ı da export et
export const getProducts = fetchProducts;