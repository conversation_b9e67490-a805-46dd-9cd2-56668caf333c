export const API_URL = process.env.EXPO_PUBLIC_API_URL || 'http://localhost:5001/api';

console.log('API_URL configured as:', API_URL);

export async function getProducts() {
    try {
        const url = `${API_URL}/products`;
        console.log(`Fetching products from: ${url}`);

        const res = await fetch(url);
        console.log('Response status:', res.status);

        if (!res.ok) {
            throw new Error(`HTTP error! status: ${res.status}`);
        }

        const data = await res.json();
        console.log('Products fetched successfully. Count:', data.length);
        console.log('First product:', data[0]);
        return data;
    } catch (error) {
        console.error('Error fetching products:', error);
        throw error; // Re-throw to let the component handle it
    }
}