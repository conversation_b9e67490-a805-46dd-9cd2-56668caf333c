import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert, TextInput } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/colors';
import { useCart } from '../../contexts/CartContext';
import { Navbar } from '../../components/Navbar';

// Safe function to format price
const formatPrice = (price) => {
  try {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2);
  } catch (error) {
    console.warn('Error formatting price:', error);
    return '0.00';
  }
};

export default function CheckoutPage() {
  const { user } = useUser();
  const router = useRouter();
  const { cartItems, getCartTotal, clearCart } = useCart();
  
  const [loading, setLoading] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [shippingInfo, setShippingInfo] = useState({
    fullName: user?.fullName || '',
    email: user?.emailAddresses?.[0]?.emailAddress || '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
  });

  const handleInputChange = (field, value) => {
    setShippingInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    const required = ['fullName', 'email', 'phone', 'address', 'city', 'postalCode'];
    for (let field of required) {
      if (!shippingInfo[field].trim()) {
        Alert.alert('Hata', `Lütfen ${getFieldLabel(field)} alanını doldurun`);
        return false;
      }
    }
    return true;
  };

  const getFieldLabel = (field) => {
    const labels = {
      fullName: 'Ad Soyad',
      email: 'E-posta',
      phone: 'Telefon',
      address: 'Adres',
      city: 'Şehir',
      postalCode: 'Posta Kodu'
    };
    return labels[field];
  };

  const handlePayment = async () => {
    if (!validateForm()) return;

    setLoading(true);
    
    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Clear cart after successful payment
      clearCart();
      
      Alert.alert(
        'Ödeme Başarılı!',
        'Siparişiniz başarıyla alındı. Kargo takip bilgileri e-posta adresinize gönderilecek.',
        [
          { 
            text: 'Tamam', 
            onPress: () => router.push('/') 
          }
        ]
      );
    } catch (error) {
      Alert.alert('Hata', 'Ödeme işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.');
    } finally {
      setLoading(false);
    }
  };

  const total = getCartTotal();
  const shipping = 15.00;
  const tax = total * 0.18;
  const grandTotal = total + shipping + tax;

  return (
    <View style={styles.container}>
      <Navbar user={user} router={router} />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color={COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.title}>Ödeme</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Order Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sipariş Özeti</Text>
          {cartItems.map((item) => (
            <View key={item.id} style={styles.orderItem}>
              <Text style={styles.itemName}>{item.name}</Text>
              <Text style={styles.itemDetails}>
                {item.quantity} x ₺{formatPrice(item.price)}
              </Text>
              <Text style={styles.itemTotal}>
                ₺{formatPrice(item.quantity * item.price)}
              </Text>
            </View>
          ))}
        </View>

        {/* Shipping Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Teslimat Bilgileri</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Ad Soyad *</Text>
            <TextInput
              style={styles.input}
              value={shippingInfo.fullName}
              onChangeText={(value) => handleInputChange('fullName', value)}
              placeholder="Ad Soyad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>E-posta *</Text>
            <TextInput
              style={styles.input}
              value={shippingInfo.email}
              onChangeText={(value) => handleInputChange('email', value)}
              placeholder="E-posta"
              keyboardType="email-address"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Telefon *</Text>
            <TextInput
              style={styles.input}
              value={shippingInfo.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              placeholder="Telefon"
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Adres *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={shippingInfo.address}
              onChangeText={(value) => handleInputChange('address', value)}
              placeholder="Adres"
              multiline
              numberOfLines={3}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Şehir *</Text>
              <TextInput
                style={styles.input}
                value={shippingInfo.city}
                onChangeText={(value) => handleInputChange('city', value)}
                placeholder="Şehir"
              />
            </View>

            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Posta Kodu *</Text>
              <TextInput
                style={styles.input}
                value={shippingInfo.postalCode}
                onChangeText={(value) => handleInputChange('postalCode', value)}
                placeholder="Posta Kodu"
                keyboardType="numeric"
              />
            </View>
          </View>
        </View>

        {/* Payment Method */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ödeme Yöntemi</Text>
          
          <TouchableOpacity
            style={[styles.paymentOption, paymentMethod === 'card' && styles.paymentOptionSelected]}
            onPress={() => setPaymentMethod('card')}
          >
            <Ionicons name="card" size={24} color={paymentMethod === 'card' ? COLORS.primary : COLORS.textLight} />
            <Text style={[styles.paymentText, paymentMethod === 'card' && styles.paymentTextSelected]}>
              Kredi/Banka Kartı
            </Text>
            {paymentMethod === 'card' && (
              <Ionicons name="checkmark-circle" size={20} color={COLORS.primary} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.paymentOption, paymentMethod === 'cash' && styles.paymentOptionSelected]}
            onPress={() => setPaymentMethod('cash')}
          >
            <Ionicons name="cash" size={24} color={paymentMethod === 'cash' ? COLORS.primary : COLORS.textLight} />
            <Text style={[styles.paymentText, paymentMethod === 'cash' && styles.paymentTextSelected]}>
              Kapıda Ödeme
            </Text>
            {paymentMethod === 'cash' && (
              <Ionicons name="checkmark-circle" size={20} color={COLORS.primary} />
            )}
          </TouchableOpacity>
        </View>

        {/* Price Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Fiyat Özeti</Text>
          
          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>Ara Toplam:</Text>
            <Text style={styles.priceValue}>₺{formatPrice(total)}</Text>
          </View>

          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>Kargo:</Text>
            <Text style={styles.priceValue}>₺{formatPrice(shipping)}</Text>
          </View>

          <View style={styles.priceRow}>
            <Text style={styles.priceLabel}>KDV (%18):</Text>
            <Text style={styles.priceValue}>₺{formatPrice(tax)}</Text>
          </View>

          <View style={[styles.priceRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Toplam:</Text>
            <Text style={styles.totalValue}>₺{formatPrice(grandTotal)}</Text>
          </View>
        </View>
      </ScrollView>

      {/* Payment Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.payButton, loading && styles.payButtonDisabled]}
          onPress={handlePayment}
          disabled={loading}
        >
          <Text style={styles.payButtonText}>
            {loading ? 'İşleniyor...' : `₺${formatPrice(grandTotal)} Öde`}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    maxWidth: 1200,
    alignSelf: 'center',
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginLeft: 15,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: COLORS.card,
    margin: 15,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 15,
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  itemName: {
    flex: 1,
    fontSize: 14,
    color: COLORS.text,
  },
  itemDetails: {
    fontSize: 12,
    color: COLORS.textLight,
    marginHorizontal: 10,
  },
  itemTotal: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
  },
  inputGroup: {
    marginBottom: 15,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 5,
  },
  input: {
    backgroundColor: COLORS.background,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    color: COLORS.text,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    marginBottom: 10,
  },
  paymentOptionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  paymentText: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: COLORS.text,
  },
  paymentTextSelected: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 5,
  },
  priceLabel: {
    fontSize: 14,
    color: COLORS.textLight,
  },
  priceValue: {
    fontSize: 14,
    color: COLORS.text,
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    paddingTop: 10,
    marginTop: 10,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  footer: {
    padding: 20,
    backgroundColor: COLORS.card,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  payButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  payButtonDisabled: {
    backgroundColor: COLORS.textLight,
    opacity: 0.6,
  },
  payButtonText: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
};
