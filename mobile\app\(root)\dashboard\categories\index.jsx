import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, RefreshControl } from 'react-native';
import { useUser, useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { useCategories } from '../../../../hooks/useCategories';
import { Navbar } from '../../../../components/Navbar';
import { Toast } from '../../../../components/Toast';
import { useToast } from '../../../../hooks/useToast';
import { ConfirmationModal } from '../../../../components/ConfirmationModal';

export default function CategoriesManagement() {
  const { user } = useUser();
  const { getToken } = useAuth();
  const router = useRouter();
  const { categories, isLoading, refreshCategories } = useCategories();
  const [refreshing, setRefreshing] = useState(false);
  const { toast, showSuccess, showError, hideToast } = useToast();
  const [deleteModal, setDeleteModal] = useState({ visible: false, category: null });

  const onRefresh = async () => {
    setRefreshing(true);
    await refreshCategories();
    setRefreshing(false);
  };

  const handleAddCategory = () => {
    router.push('/dashboard/categories/add');
  };

  const handleEditCategory = (category) => {
    console.log('Edit category clicked:', category);
    router.push(`/dashboard/categories/edit/${category.id}`);
  };

  const handleDeleteCategory = async (category) => {
    console.log('Delete category clicked:', category);
    setDeleteModal({ visible: true, category });
  };

  const confirmDeleteCategory = async () => {
    if (!deleteModal.category) return;

    try {
      const token = await getToken();
      const response = await fetch(`${API_URL}/categories/${deleteModal.category.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        showSuccess('Kategori başarıyla silindi');
        refreshCategories();
      } else {
        const errorData = await response.json();
        showError(errorData.message || 'Kategori silinirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      showError('Kategori silinirken bir hata oluştu');
    } finally {
      setDeleteModal({ visible: false, category: null });
    }
  };

  const renderCategoryItem = ({ item }) => (
    <View style={styles.categoryItem}>
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
        <Text style={styles.categoryDate}>
          Oluşturulma: {new Date(item.created_at).toLocaleDateString('tr-TR')}
        </Text>
      </View>
      <View style={styles.categoryActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => {
            console.log('Edit button clicked for category:', item.id);
            handleEditCategory(item);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="pencil" size={16} color={COLORS.white} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => {
            console.log('Delete button clicked for category:', item.id);
            handleDeleteCategory(item);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="trash" size={16} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <View style={styles.container}>
        <Navbar user={user} router={router} />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Kategoriler yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Navbar user={user} router={router} />

      <View style={styles.header}>
        <Text style={styles.title}>Kategori Yönetimi</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            console.log('Add category button clicked');
            handleAddCategory();
          }}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={24} color={COLORS.white} />
          <Text style={styles.addButtonText}>Yeni Kategori</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={categories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="folder-open-outline" size={64} color={COLORS.textLight} />
            <Text style={styles.emptyText}>Henüz kategori bulunmuyor</Text>
            <TouchableOpacity
              style={styles.emptyButton}
              onPress={() => {
                console.log('Empty add category button clicked');
                handleAddCategory();
              }}
              activeOpacity={0.8}
            >
              <Text style={styles.emptyButtonText}>İlk Kategoriyi Ekle</Text>
            </TouchableOpacity>
          </View>
        }
      />

      <Toast
        visible={toast.visible}
        message={toast.message}
        type={toast.type}
        onHide={hideToast}
      />

      <ConfirmationModal
        visible={deleteModal.visible}
        title="Kategoriyi Sil"
        message={`"${deleteModal.category?.name}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
        confirmText="Sil"
        cancelText="İptal"
        onConfirm={confirmDeleteCategory}
        onCancel={() => setDeleteModal({ visible: false, category: null })}
        type="danger"
      />
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addButtonText: {
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 8,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.card,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  categoryDate: {
    fontSize: 12,
    color: COLORS.textLight,
  },
  categoryActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
    elevation: 5,
  },
  editButton: {
    backgroundColor: COLORS.warning,
  },
  deleteButton: {
    backgroundColor: COLORS.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.textLight,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.textLight,
  },
};
