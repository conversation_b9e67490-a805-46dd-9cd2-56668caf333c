import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { styled } from 'nativewind';
import * as Sentry from '@sentry/react-native';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledScrollView = styled(ScrollView);

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to Sentry
    const errorId = Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack
        }
      }
    });

    this.setState({
      error,
      errorInfo,
      errorId
    });

    // Log to console in development
    if (__DEV__) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReportError = () => {
    if (this.state.errorId) {
      // You can implement custom error reporting here
      console.log('Error reported with ID:', this.state.errorId);
    }
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback, showDetails = __DEV__ } = this.props;

      // Custom fallback component
      if (Fallback) {
        return (
          <Fallback
            error={this.state.error}
            errorInfo={this.state.errorInfo}
            onRetry={this.handleRetry}
          />
        );
      }

      // Default error UI
      return (
        <StyledView className="flex-1 justify-center items-center p-6 bg-gray-50">
          <StyledView className="bg-white rounded-lg p-6 shadow-lg max-w-sm w-full">
            {/* Error Icon */}
            <StyledView className="items-center mb-4">
              <StyledView className="w-16 h-16 bg-red-100 rounded-full items-center justify-center mb-3">
                <StyledText className="text-red-500 text-2xl">⚠️</StyledText>
              </StyledView>
              <StyledText className="text-xl font-bold text-gray-800 text-center">
                Oops! Something went wrong
              </StyledText>
            </StyledView>

            {/* Error Message */}
            <StyledText className="text-gray-600 text-center mb-6">
              We're sorry, but something unexpected happened. Please try again.
            </StyledText>

            {/* Action Buttons */}
            <StyledView className="space-y-3">
              <StyledTouchableOpacity
                className="bg-blue-500 rounded-lg py-3 px-4"
                onPress={this.handleRetry}
              >
                <StyledText className="text-white font-medium text-center">
                  Try Again
                </StyledText>
              </StyledTouchableOpacity>

              {this.state.errorId && (
                <StyledTouchableOpacity
                  className="bg-gray-200 rounded-lg py-3 px-4"
                  onPress={this.handleReportError}
                >
                  <StyledText className="text-gray-700 font-medium text-center">
                    Report Issue
                  </StyledText>
                </StyledTouchableOpacity>
              )}
            </StyledView>

            {/* Error Details (Development only) */}
            {showDetails && this.state.error && (
              <StyledView className="mt-6 pt-4 border-t border-gray-200">
                <StyledText className="text-sm font-medium text-gray-700 mb-2">
                  Error Details (Development):
                </StyledText>
                <StyledScrollView className="max-h-32 bg-gray-100 rounded p-2">
                  <StyledText className="text-xs text-gray-600 font-mono">
                    {this.state.error.toString()}
                  </StyledText>
                  {this.state.errorInfo?.componentStack && (
                    <StyledText className="text-xs text-gray-500 font-mono mt-2">
                      {this.state.errorInfo.componentStack}
                    </StyledText>
                  )}
                </StyledScrollView>
              </StyledView>
            )}

            {/* Error ID */}
            {this.state.errorId && (
              <StyledView className="mt-4 pt-4 border-t border-gray-200">
                <StyledText className="text-xs text-gray-500 text-center">
                  Error ID: {this.state.errorId}
                </StyledText>
              </StyledView>
            )}
          </StyledView>
        </StyledView>
      );
    }

    return this.props.children;
  }
}

// HOC for wrapping components with error boundary
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for error handling
export const useErrorHandler = () => {
  const [error, setError] = React.useState(null);

  const handleError = React.useCallback((error) => {
    setError(error);
    Sentry.captureException(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, clearError, error };
};

// Async error boundary for handling promise rejections
export const AsyncErrorBoundary = ({ children, onError }) => {
  React.useEffect(() => {
    const handleUnhandledRejection = (event) => {
      if (onError) {
        onError(event.reason);
      } else {
        Sentry.captureException(event.reason);
      }
    };

    // Note: React Native doesn't have window.addEventListener
    // This is more for web compatibility
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', handleUnhandledRejection);
      return () => {
        window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      };
    }
  }, [onError]);

  return <ErrorBoundary>{children}</ErrorBoundary>;
};

export default ErrorBoundary;
