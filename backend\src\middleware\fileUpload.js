import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { AppError } from './errorHandler.js';
import logger from '../config/logger.js';

// File type configurations
const FILE_TYPES = {
  images: {
    mimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
    maxSize: 5 * 1024 * 1024, // 5MB
    destination: 'uploads/images'
  },
  documents: {
    mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    extensions: ['.pdf', '.doc', '.docx'],
    maxSize: 10 * 1024 * 1024, // 10MB
    destination: 'uploads/documents'
  },
  avatars: {
    mimeTypes: ['image/jpeg', 'image/jpg', 'image/png'],
    extensions: ['.jpg', '.jpeg', '.png'],
    maxSize: 2 * 1024 * 1024, // 2MB
    destination: 'uploads/avatars'
  }
};

// Ensure upload directories exist
Object.values(FILE_TYPES).forEach(config => {
  const dir = path.join(process.cwd(), config.destination);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Storage configuration
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const fileType = req.fileType || 'images';
    const config = FILE_TYPES[fileType];
    
    if (!config) {
      return cb(new AppError('Invalid file type configuration', 400));
    }

    cb(null, config.destination);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname).toLowerCase();
    const name = file.fieldname + '-' + uniqueSuffix + ext;
    cb(null, name);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  const fileType = req.fileType || 'images';
  const config = FILE_TYPES[fileType];

  if (!config) {
    return cb(new AppError('Invalid file type configuration', 400));
  }

  // Check MIME type
  if (!config.mimeTypes.includes(file.mimetype)) {
    return cb(new AppError(`Invalid file type. Allowed types: ${config.mimeTypes.join(', ')}`, 400));
  }

  // Check file extension
  const ext = path.extname(file.originalname).toLowerCase();
  if (!config.extensions.includes(ext)) {
    return cb(new AppError(`Invalid file extension. Allowed extensions: ${config.extensions.join(', ')}`, 400));
  }

  cb(null, true);
};

// Create multer instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // Default 10MB, will be overridden by middleware
    files: 5 // Maximum 5 files per request
  }
});

// Middleware factory for different file types
const createFileUploadMiddleware = (fileType, fieldName = 'file', multiple = false) => {
  return (req, res, next) => {
    const config = FILE_TYPES[fileType];
    
    if (!config) {
      return next(new AppError('Invalid file type configuration', 400));
    }

    // Set file type for storage and filter
    req.fileType = fileType;

    // Update file size limit
    upload.limits.fileSize = config.maxSize;

    // Choose upload method based on multiple flag
    const uploadMethod = multiple ? 
      upload.array(fieldName, 5) : 
      upload.single(fieldName);

    uploadMethod(req, res, (err) => {
      if (err instanceof multer.MulterError) {
        if (err.code === 'LIMIT_FILE_SIZE') {
          return next(new AppError(`File too large. Maximum size: ${(config.maxSize / (1024 * 1024)).toFixed(1)}MB`, 400));
        }
        if (err.code === 'LIMIT_FILE_COUNT') {
          return next(new AppError('Too many files. Maximum 5 files allowed', 400));
        }
        if (err.code === 'LIMIT_UNEXPECTED_FILE') {
          return next(new AppError(`Unexpected field name. Expected: ${fieldName}`, 400));
        }
        return next(new AppError(`Upload error: ${err.message}`, 400));
      }
      
      if (err) {
        return next(err);
      }

      // Validate uploaded files
      if (req.file) {
        validateSingleFile(req.file, config);
      }
      
      if (req.files && req.files.length > 0) {
        req.files.forEach(file => validateSingleFile(file, config));
      }

      // Log successful upload
      const fileCount = req.files ? req.files.length : (req.file ? 1 : 0);
      if (fileCount > 0) {
        logger.info(`File upload successful: ${fileCount} file(s) uploaded`, {
          fileType,
          fieldName,
          files: req.files || [req.file]
        });
      }

      next();
    });
  };
};

// Validate individual file
const validateSingleFile = (file, config) => {
  // Additional security checks
  
  // Check for null bytes (potential security risk)
  if (file.originalname.includes('\0')) {
    throw new AppError('Invalid filename: contains null bytes', 400);
  }

  // Check filename length
  if (file.originalname.length > 255) {
    throw new AppError('Filename too long. Maximum 255 characters', 400);
  }

  // Check for directory traversal attempts
  if (file.originalname.includes('..') || file.originalname.includes('/') || file.originalname.includes('\\')) {
    throw new AppError('Invalid filename: contains path traversal characters', 400);
  }

  // Verify file actually exists and has content
  if (!fs.existsSync(file.path) || file.size === 0) {
    throw new AppError('Invalid file: file is empty or corrupted', 400);
  }
};

// Specific middleware for different use cases
const uploadImage = createFileUploadMiddleware('images', 'image', false);
const uploadImages = createFileUploadMiddleware('images', 'images', true);
const uploadAvatar = createFileUploadMiddleware('avatars', 'avatar', false);
const uploadDocument = createFileUploadMiddleware('documents', 'document', false);

// File cleanup utility
const cleanupFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info(`File cleaned up: ${filePath}`);
    }
  } catch (error) {
    logger.error(`Failed to cleanup file: ${filePath}`, error);
  }
};

// Cleanup middleware for failed requests
const cleanupOnError = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // If response is an error and files were uploaded, clean them up
    if (res.statusCode >= 400) {
      if (req.file) {
        cleanupFile(req.file.path);
      }
      if (req.files && req.files.length > 0) {
        req.files.forEach(file => cleanupFile(file.path));
      }
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

// Get file URL helper
const getFileUrl = (file, baseUrl = process.env.BASE_URL || 'http://localhost:5001') => {
  if (!file) return null;
  
  const relativePath = file.path.replace(process.cwd(), '').replace(/\\/g, '/');
  return `${baseUrl}${relativePath}`;
};

export {
  uploadImage,
  uploadImages,
  uploadAvatar,
  uploadDocument,
  cleanupFile,
  cleanupOnError,
  getFileUrl,
  FILE_TYPES
};
