import { useUser } from '@clerk/clerk-expo';
import { ScrollView, View, Alert, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import { useCart } from '../../contexts/CartContext';
import { Box } from "@/components/ui/box";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Image } from "@/components/ui/image";
import { Button, ButtonText } from "@/components/ui/button";
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/colors';

// Safe function to get product image
const getProductImage = (product) => {
  try {
    if (!product.images) {
      return 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
    }

    if (typeof product.images === 'string') {
      try {
        const parsedImages = JSON.parse(product.images);
        return Array.isArray(parsedImages) && parsedImages.length > 0
          ? parsedImages[0]
          : 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
      } catch (parseError) {
        return product.images.startsWith('http')
          ? product.images
          : 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
      }
    }

    if (Array.isArray(product.images) && product.images.length > 0) {
      return product.images[0];
    }

    return 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
  } catch (error) {
    console.warn('Error getting product image:', error);
    return 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
  }
};

// Safe function to format price
const formatPrice = (price) => {
  try {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2);
  } catch (error) {
    console.warn('Error formatting price:', error);
    return '0.00';
  }
};

const CartItem = ({ item, onUpdateQuantity, onRemove }) => {
  const router = useRouter();

  const handleProductPress = () => {
    router.push(`/product/${item.id}`);
  };

  const increaseQuantity = () => {
    onUpdateQuantity(item.id, item.quantity + 1);
  };

  const decreaseQuantity = () => {
    if (item.quantity > 1) {
      onUpdateQuantity(item.id, item.quantity - 1);
    }
  };

  const handleRemove = () => {
    Alert.alert(
      'Ürünü Kaldır',
      `${item.name} sepetten kaldırılsın mı?`,
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Kaldır', style: 'destructive', onPress: () => onRemove(item.id) }
      ]
    );
  };

  return (
    <Box className="bg-white p-4 rounded-lg mb-3 shadow-sm border border-gray-200">
      <HStack className="space-x-4">
        {/* Product Image */}
        <TouchableOpacity onPress={handleProductPress}>
          <Image
            source={{ uri: getProductImage(item) }}
            alt={item.name}
            style={styles.cartItemImage}
          />
        </TouchableOpacity>

        {/* Product Info */}
        <Box className="flex-1">
          <TouchableOpacity onPress={handleProductPress}>
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              {item.name}
            </Text>
          </TouchableOpacity>
          
          <Text className="text-xl font-bold text-green-600 mb-3">
            ₺{formatPrice(item.price)}
          </Text>

          {/* Quantity Controls */}
          <HStack className="items-center justify-between">
            <HStack className="items-center space-x-3">
              <TouchableOpacity 
                style={styles.quantityButton} 
                onPress={decreaseQuantity}
                disabled={item.quantity <= 1}
              >
                <Ionicons name="remove" size={16} color={item.quantity <= 1 ? COLORS.textLight : COLORS.text} />
              </TouchableOpacity>
              
              <Text className="text-lg font-semibold min-w-[30px] text-center">
                {item.quantity}
              </Text>
              
              <TouchableOpacity 
                style={styles.quantityButton} 
                onPress={increaseQuantity}
              >
                <Ionicons name="add" size={16} color={COLORS.text} />
              </TouchableOpacity>
            </HStack>

            {/* Remove Button */}
            <TouchableOpacity 
              style={styles.removeButton}
              onPress={handleRemove}
            >
              <Ionicons name="trash-outline" size={20} color={COLORS.error || '#FF4444'} />
            </TouchableOpacity>
          </HStack>

          {/* Total Price for this item */}
          <Text className="text-right text-gray-600 mt-2">
            Toplam: ₺{formatPrice(item.price * item.quantity)}
          </Text>
        </Box>
      </HStack>
    </Box>
  );
};

export default function CartPage() {
  const { user } = useUser();
  const { cartItems, updateQuantity, removeFromCart, clearCart, getCartTotal } = useCart();
  const router = useRouter();

  const handleClearCart = () => {
    Alert.alert(
      'Sepeti Temizle',
      'Sepetteki tüm ürünler kaldırılsın mı?',
      [
        { text: 'İptal', style: 'cancel' },
        { text: 'Temizle', style: 'destructive', onPress: clearCart }
      ]
    );
  };

  const handleCheckout = () => {
    Alert.alert('Ödeme', 'Ödeme sistemi yakında aktif olacak!');
  };

  if (cartItems.length === 0) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center px-6">
          <Ionicons name="cart-outline" size={80} color={COLORS.textLight} />
          <Heading size="xl" className="text-gray-500 mt-4 mb-2 text-center">
            Sepetiniz Boş
          </Heading>
          <Text className="text-gray-400 text-center mb-6">
            Alışverişe başlamak için ürünleri sepete ekleyin
          </Text>
          <Button onPress={() => router.push('/')}>
            <ButtonText>Alışverişe Başla</ButtonText>
          </Button>
        </Box>
      </View>
    );
  }

  const totalAmount = getCartTotal();

  return (
    <View style={styles.container}>
      {/* Header */}
      <Box className="bg-white p-4 border-b border-gray-200">
        <HStack className="items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <Heading size="lg" className="text-gray-900">
            Sepetim ({cartItems.length})
          </Heading>
          
          <TouchableOpacity onPress={handleClearCart}>
            <Text className="text-red-500 font-semibold">Temizle</Text>
          </TouchableOpacity>
        </HStack>
      </Box>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Box className="p-4">
          {cartItems.map((item) => (
            <CartItem
              key={item.id}
              item={item}
              onUpdateQuantity={updateQuantity}
              onRemove={removeFromCart}
            />
          ))}
        </Box>
      </ScrollView>

      {/* Bottom Summary */}
      <Box className="bg-white p-4 border-t border-gray-200">
        <VStack className="space-y-4">
          {/* Total */}
          <HStack className="items-center justify-between">
            <Text className="text-lg font-semibold text-gray-900">
              Toplam Tutar:
            </Text>
            <Text className="text-2xl font-bold text-green-600">
              ₺{formatPrice(totalAmount)}
            </Text>
          </HStack>

          {/* Checkout Button */}
          <Button 
            className="bg-green-600"
            onPress={handleCheckout}
          >
            <ButtonText>Ödemeye Geç</ButtonText>
          </Button>
        </VStack>
      </Box>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  cartItemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  removeButton: {
    padding: 8,
  },
};
