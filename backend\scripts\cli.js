#!/usr/bin/env node

import { program } from 'commander';
import dotenv from 'dotenv';
import MigrationRunner from '../src/migrations/migrationRunner.js';
import Seeder from '../src/seeders/seeder.js';
import DatabaseBackup from '../src/utils/backup.js';
import queryOptimizer from '../src/utils/queryOptimizer.js';
import logger from '../src/config/logger.js';

// Load environment variables
dotenv.config();

// Initialize services
const migrationRunner = new MigrationRunner();
const seeder = new Seeder();
const backup = new DatabaseBackup();

program
  .name('wallet-cli')
  .description('Wallet Application CLI Tools')
  .version('1.0.0');

// Migration commands
const migrationCmd = program
  .command('migration')
  .description('Database migration commands');

migrationCmd
  .command('run')
  .description('Run pending migrations')
  .action(async () => {
    try {
      console.log('Running migrations...');
      await migrationRunner.runMigrations();
      console.log('✅ Migrations completed successfully');
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    }
  });

migrationCmd
  .command('rollback')
  .description('Rollback migrations')
  .option('-s, --steps <number>', 'Number of migrations to rollback', '1')
  .action(async (options) => {
    try {
      const steps = parseInt(options.steps);
      console.log(`Rolling back ${steps} migration(s)...`);
      await migrationRunner.rollback(steps);
      console.log('✅ Rollback completed successfully');
    } catch (error) {
      console.error('❌ Rollback failed:', error.message);
      process.exit(1);
    }
  });

migrationCmd
  .command('create <name>')
  .description('Create a new migration')
  .action(async (name) => {
    try {
      const filename = await migrationRunner.createMigration(name);
      console.log(`✅ Migration created: ${filename}`);
    } catch (error) {
      console.error('❌ Migration creation failed:', error.message);
      process.exit(1);
    }
  });

// Seeder commands
const seederCmd = program
  .command('seed')
  .description('Database seeding commands');

seederCmd
  .command('run')
  .description('Run pending seeders')
  .option('-f, --force', 'Force run all seeders')
  .action(async (options) => {
    try {
      console.log('Running seeders...');
      await seeder.runSeeders(options.force);
      console.log('✅ Seeders completed successfully');
    } catch (error) {
      console.error('❌ Seeding failed:', error.message);
      process.exit(1);
    }
  });

seederCmd
  .command('reset')
  .description('Reset seed history')
  .action(async () => {
    try {
      await seeder.resetSeeders();
      console.log('✅ Seed history reset successfully');
    } catch (error) {
      console.error('❌ Reset failed:', error.message);
      process.exit(1);
    }
  });

seederCmd
  .command('create <name>')
  .description('Create a new seeder')
  .action(async (name) => {
    try {
      const filename = await seeder.createSeeder(name);
      console.log(`✅ Seeder created: ${filename}`);
    } catch (error) {
      console.error('❌ Seeder creation failed:', error.message);
      process.exit(1);
    }
  });

// Backup commands
const backupCmd = program
  .command('backup')
  .description('Database backup commands');

backupCmd
  .command('create')
  .description('Create a database backup')
  .option('-t, --type <type>', 'Backup type (manual, auto)', 'manual')
  .action(async (options) => {
    try {
      console.log('Creating backup...');
      const result = await backup.createBackup(options.type);
      console.log(`✅ Backup created: ${result.filename} (${(result.size / (1024 * 1024)).toFixed(2)} MB)`);
    } catch (error) {
      console.error('❌ Backup creation failed:', error.message);
      process.exit(1);
    }
  });

backupCmd
  .command('restore <filename>')
  .description('Restore from a backup')
  .action(async (filename) => {
    try {
      console.log(`Restoring from backup: ${filename}`);
      await backup.restoreBackup(filename);
      console.log('✅ Backup restored successfully');
    } catch (error) {
      console.error('❌ Backup restoration failed:', error.message);
      process.exit(1);
    }
  });

backupCmd
  .command('list')
  .description('List available backups')
  .action(async () => {
    try {
      const backups = await backup.listBackups();
      if (backups.length === 0) {
        console.log('No backups found');
        return;
      }

      console.log('\nAvailable backups:');
      console.log('==================');
      backups.forEach(backup => {
        const sizeMB = (backup.size / (1024 * 1024)).toFixed(2);
        console.log(`${backup.filename} - ${sizeMB} MB - ${backup.created.toISOString()} (${backup.type})`);
      });
    } catch (error) {
      console.error('❌ Failed to list backups:', error.message);
      process.exit(1);
    }
  });

backupCmd
  .command('stats')
  .description('Show backup statistics')
  .action(async () => {
    try {
      const stats = await backup.getBackupStats();
      console.log('\nBackup Statistics:');
      console.log('==================');
      console.log(`Total backups: ${stats.totalBackups}`);
      console.log(`Total size: ${stats.totalSizeMB} MB`);
      console.log(`Automatic backups: ${stats.automaticBackups}`);
      console.log(`Manual backups: ${stats.manualBackups}`);
      if (stats.newestBackup) {
        console.log(`Newest backup: ${stats.newestBackup.toISOString()}`);
      }
      if (stats.oldestBackup) {
        console.log(`Oldest backup: ${stats.oldestBackup.toISOString()}`);
      }
    } catch (error) {
      console.error('❌ Failed to get backup stats:', error.message);
      process.exit(1);
    }
  });

// Database optimization commands
const dbCmd = program
  .command('db')
  .description('Database management commands');

dbCmd
  .command('optimize')
  .description('Optimize database performance')
  .action(async () => {
    try {
      console.log('Optimizing database...');
      const optimizations = await queryOptimizer.optimizeDatabase();
      console.log('✅ Database optimization completed:');
      optimizations.forEach(opt => console.log(`  - ${opt}`));
    } catch (error) {
      console.error('❌ Database optimization failed:', error.message);
      process.exit(1);
    }
  });

dbCmd
  .command('stats')
  .description('Show database statistics')
  .action(async () => {
    try {
      console.log('Analyzing database...');
      const stats = await queryOptimizer.analyzeTableStats();
      
      console.log('\nDatabase Statistics:');
      console.log('===================');
      
      Object.entries(stats).forEach(([table, data]) => {
        console.log(`\n${table.toUpperCase()}:`);
        if (data.error) {
          console.log(`  Error: ${data.error}`);
        } else if (Array.isArray(data)) {
          data.slice(0, 5).forEach(row => {
            console.log(`  ${row.attname}: ${row.n_distinct} distinct values`);
          });
        }
      });
    } catch (error) {
      console.error('❌ Failed to get database stats:', error.message);
      process.exit(1);
    }
  });

dbCmd
  .command('slow-queries')
  .description('Show slow queries')
  .action(async () => {
    try {
      const slowQueries = await queryOptimizer.getSlowQueries();
      
      if (slowQueries.length === 0) {
        console.log('No slow queries found');
        return;
      }

      console.log('\nSlow Queries:');
      console.log('=============');
      
      slowQueries.forEach((query, index) => {
        console.log(`\n${index + 1}. Mean time: ${query.mean_time.toFixed(2)}ms`);
        console.log(`   Calls: ${query.calls}`);
        console.log(`   Query: ${query.query.substring(0, 100)}...`);
      });
    } catch (error) {
      console.error('❌ Failed to get slow queries:', error.message);
      process.exit(1);
    }
  });

// Performance monitoring
const perfCmd = program
  .command('perf')
  .description('Performance monitoring commands');

perfCmd
  .command('query-stats')
  .description('Show query performance statistics')
  .action(async () => {
    try {
      const stats = queryOptimizer.getQueryStats();
      
      if (Object.keys(stats).length === 0) {
        console.log('No query statistics available');
        return;
      }

      console.log('\nQuery Performance Statistics:');
      console.log('============================');
      
      Object.entries(stats).forEach(([queryName, data]) => {
        console.log(`\n${queryName}:`);
        console.log(`  Executions: ${data.count}`);
        console.log(`  Average time: ${data.avgTime}ms`);
        console.log(`  Min time: ${data.minTime}ms`);
        console.log(`  Max time: ${data.maxTime}ms`);
        console.log(`  Error rate: ${data.errorRate}%`);
        console.log(`  Last executed: ${data.lastExecuted}`);
      });
    } catch (error) {
      console.error('❌ Failed to get query stats:', error.message);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();
