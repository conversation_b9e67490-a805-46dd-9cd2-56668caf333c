// Seeder: Create default users
// Created: 2024-12-01T00:00:00.000Z

import bcrypt from 'bcrypt';

export const run = async (sql) => {
  console.log('Seeding users...');

  // Hash passwords
  const adminPassword = await bcrypt.hash('admin123', 12);
  const userPassword = await bcrypt.hash('user123', 12);
  const sellerPassword = await bcrypt.hash('seller123', 12);

  // Insert default users
  await sql`
    INSERT INTO users (name, email, username, password, role, status, email_verified)
    VALUES 
      ('Admin User', '<EMAIL>', 'admin', ${adminPassword}, 'admin', 'active', true),
      ('<PERSON>', '<EMAIL>', 'johndoe', ${userPassword}, 'user', 'active', true),
      ('<PERSON>', '<EMAIL>', 'janesmith', ${userPassword}, 'user', 'active', true),
      ('Seller One', '<EMAIL>', 'seller1', ${sellerPassword}, 'seller', 'active', true),
      ('Seller Two', '<EMAIL>', 'seller2', ${sellerPassword}, 'seller', 'active', true),
      ('Test User', '<EMAIL>', 'testuser', ${userPassword}, 'user', 'active', false)
    ON CONFLICT (email) DO NOTHING
  `;

  console.log('Users seeded successfully');
};
