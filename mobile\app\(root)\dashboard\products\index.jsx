import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, TouchableOpacity, RefreshControl, Image } from 'react-native';
import { useUser, useAuth } from '@clerk/clerk-expo';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { useProducts } from '../../../../hooks/useProducts';
import { Navbar } from '../../../../components/Navbar';
import { ConfirmationModal } from '../../../../components/ConfirmationModal';
import { Toast } from '../../../../components/Toast';
import { useToast } from '../../../../hooks/useToast';

export default function ProductsManagement() {
  const { user } = useUser();
  const { getToken } = useAuth();
  const router = useRouter();
  const { refresh } = useLocalSearchParams();
  const { products, isLoading, error, refreshProducts } = useProducts();
  const [refreshing, setRefreshing] = useState(false);
  const { toast, showSuccess, showError, hideToast } = useToast();
  const [deleteModal, setDeleteModal] = useState({ visible: false, product: null });

  React.useEffect(() => {
    if (refresh) {
      console.log('Refresh triggered from EditProduct');
      refreshProducts();
    }
  }, [refresh, refreshProducts]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await refreshProducts();
    setRefreshing(false);
  }, [refreshProducts]);

  const handleAddProduct = useCallback(() => {
    console.log('Add product button clicked');
    router.push('/dashboard/products/add');
  }, [router]);

  const handleEditProduct = useCallback((product) => {
    console.log('Edit button clicked for product:', product.id);
    router.push(`/dashboard/products/edit/${product.id}`);
  }, [router]);

  const handleDeleteProduct = useCallback((product) => {
    console.log('Delete product clicked:', { id: product.id, name: product.name });
    setDeleteModal({ visible: true, product });
  }, []);

  const confirmDeleteProduct = useCallback(async () => {
    console.log('Confirm delete for product:', deleteModal.product?.id);
    if (!deleteModal.product || !deleteModal.product.id) {
      console.error('No product selected for deletion');
      showError('Silinecek ürün bulunamadı');
      setDeleteModal({ visible: false, product: null });
      return;
    }

    try {
      const token = await getToken();
      console.log('Sending DELETE request to:', `${API_URL}/products/${deleteModal.product.id}`);
      const response = await fetch(`${API_URL}/products/${deleteModal.product.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });
      console.log('Response status:', response.status);
      if (response.ok) {
        showSuccess('Ürün başarıyla silindi');
        await refreshProducts();
      } else {
        const errorData = await response.json();
        console.error('Delete error:', errorData);
        showError(errorData.message || 'Ürün silinirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      showError('Ürün silinirken bir hata oluştu: ' + error.message);
    } finally {
      setDeleteModal({ visible: false, product: null });
    }
  }, [deleteModal.product, showError, showSuccess, refreshProducts]);

  const getProductImage = useCallback((product) => {
    if (product.images && product.images.length > 0) {
      return product.images[0];
    }
    return 'https://via.placeholder.com/60x60/f0f0f0/999999?text=No+Image';
  }, []);

  const renderProductItem = React.memo(({ item }) => (
    <View style={styles.productItem}>
      <Image source={{ uri: getProductImage(item) }} style={styles.productImage} />
      <View style={styles.productInfo}>
        <Text style={styles.productName} numberOfLines={2}>{item.name}</Text>
        <Text style={styles.productPrice}>₺{item.price.toFixed(2)}</Text>
        <Text style={styles.productStock}>Stok: {item.stock || 0}</Text>
        <Text style={styles.productDate}>
          {new Date(item.created_at).toLocaleDateString('tr-TR')}
        </Text>
      </View>
      <View style={styles.productActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => handleEditProduct(item)}
          activeOpacity={0.7}
        >
          <Ionicons name="pencil" size={16} color={COLORS.white} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDeleteProduct(item)}
          activeOpacity={0.7}
        >
          <Ionicons name="trash" size={16} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  ));

  return (
    <View style={styles.container}>
      <Navbar user={user} router={router} />
      <View style={styles.header}>
        <Text style={styles.title}>Ürün Yönetimi</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={handleAddProduct}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={24} color={COLORS.white} />
          <Text style={styles.addButtonText}>Yeni Ürün</Text>
        </TouchableOpacity>
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Hata: {error}</Text>
        </View>
      )}

      <FlatList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id.toString()}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        extraData={products}
        initialNumToRender={10}
        maxToRenderPerBatch={10}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="cube-outline" size={64} color={COLORS.textLight} />
            <Text style={styles.emptyText}>Henüz ürün bulunmuyor</Text>
            <TouchableOpacity
              style={styles.emptyButton}
              onPress={handleAddProduct}
              activeOpacity={0.8}
            >
              <Text style={styles.emptyButtonText}>İlk Ürünü Ekle</Text>
            </TouchableOpacity>
          </View>
        }
      />

      <Toast
        visible={toast.visible}
        message={toast.message}
        type={toast.type}
        onHide={hideToast}
      />

      <ConfirmationModal
        visible={deleteModal.visible}
        title="Ürünü Sil"
        message={`"${deleteModal.product?.name || ''}" ürününü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
        confirmText="Sil"
        cancelText="İptal"
        onConfirm={confirmDeleteProduct}
        onCancel={() => setDeleteModal({ visible: false, product: null })}
        type="danger"
      />
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
  },
  addButtonText: {
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 8,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  productItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.card,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: COLORS.background,
  },
  productInfo: {
    flex: 1,
    marginLeft: 12,
  },
  productName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 2,
  },
  productStock: {
    fontSize: 12,
    color: COLORS.textLight,
    marginBottom: 2,
  },
  productDate: {
    fontSize: 11,
    color: COLORS.textLight,
  },
  productActions: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center ',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: COLORS.warning,
  },
  deleteButton: {
    backgroundColor: COLORS.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.textLight,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
  errorContainer: {
    padding: 10,
    backgroundColor: COLORS.error,
    margin: 10,
    borderRadius: 8,
  },
  errorText: {
    color: COLORS.white,
    fontSize: 14,
    textAlign: 'center',
  },
};
