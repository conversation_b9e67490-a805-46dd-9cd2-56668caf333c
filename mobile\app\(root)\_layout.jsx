import { View, Text } from 'react-native'
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import React from 'react'
import { Stack } from 'expo-router'
import { useUser } from '@clerk/clerk-expo';
import { Redirect } from 'expo-router';

export default function RootLayout() {
    const { isSignedIn, isLoaded } = useUser();

    if (!isLoaded) {
      console.log('Clerk loading...');
      return (
        <GluestackUIProvider>
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text>Yükleniyor...</Text>
          </View>
        </GluestackUIProvider>
      );
    }

    if (!isSignedIn) {
      console.log('User not signed in, redirecting to sign-in');
      return <Redirect href={'/(auth)/sign-in'} />
    }

    console.log('User is signed in, showing main app');
  return <GluestackUIProvider>
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          title: "Home",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="product/[id]"
        options={{
          title: "Product Details",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="cart"
        options={{
          title: "Cart",
          headerShown: true,
        }}
      />
    </Stack>
    </GluestackUIProvider>;
}
