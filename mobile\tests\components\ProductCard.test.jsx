import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import ProductCard from '../../components/ProductListItem';

describe('ProductCard Component', () => {
  const mockProduct = {
    id: 1,
    name: 'Test Product',
    description: 'Test Description',
    price: 99.99,
    original_price: 129.99,
    discount_percentage: 23,
    images: ['https://example.com/image.jpg'],
    category: 'electronics',
    stock: 10,
    featured: true
  };

  const mockProps = {
    product: mockProduct,
    onAddToCart: jest.fn(),
    onAddToWishlist: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders product information correctly', () => {
    const { getByText } = render(<ProductCard {...mockProps} />);
    
    expect(getByText('Test Product')).toBeTruthy();
    expect(getByText('Test Description')).toBeTruthy();
    expect(getByText('$99.99')).toBeTruthy();
  });

  it('displays discount information when available', () => {
    const { getByText } = render(<ProductCard {...mockProps} />);
    
    expect(getByText('$129.99')).toBeTruthy(); // original price
    expect(getByText('23% OFF')).toBeTruthy(); // discount
  });

  it('calls onAddToCart when add to cart button is pressed', () => {
    const { getByTestId } = render(<ProductCard {...mockProps} />);
    
    const addToCartButton = getByTestId('add-to-cart-button');
    fireEvent.press(addToCartButton);
    
    expect(mockProps.onAddToCart).toHaveBeenCalledWith(mockProduct);
  });

  it('calls onAddToWishlist when wishlist button is pressed', () => {
    const { getByTestId } = render(<ProductCard {...mockProps} />);
    
    const wishlistButton = getByTestId('wishlist-button');
    fireEvent.press(wishlistButton);
    
    expect(mockProps.onAddToWishlist).toHaveBeenCalledWith(mockProduct);
  });

  it('shows out of stock message when stock is 0', () => {
    const outOfStockProduct = { ...mockProduct, stock: 0 };
    const { getByText } = render(
      <ProductCard {...mockProps} product={outOfStockProduct} />
    );
    
    expect(getByText('Out of Stock')).toBeTruthy();
  });

  it('displays featured badge for featured products', () => {
    const { getByText } = render(<ProductCard {...mockProps} />);
    
    expect(getByText('Featured')).toBeTruthy();
  });

  it('does not display featured badge for non-featured products', () => {
    const nonFeaturedProduct = { ...mockProduct, featured: false };
    const { queryByText } = render(
      <ProductCard {...mockProps} product={nonFeaturedProduct} />
    );
    
    expect(queryByText('Featured')).toBeNull();
  });

  it('handles missing image gracefully', () => {
    const productWithoutImage = { ...mockProduct, images: [] };
    const { getByTestId } = render(
      <ProductCard {...mockProps} product={productWithoutImage} />
    );
    
    // Should render placeholder image
    expect(getByTestId('product-image')).toBeTruthy();
  });
});
