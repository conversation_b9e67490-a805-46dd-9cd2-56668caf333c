import { useUser } from '@clerk/clerk-expo';
import {  View, Alert, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useState, useEffect } from 'react';
import { useCart } from '../../../contexts/CartContext';
import { Box } from "@/components/ui/box";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Image } from "@/components/ui/image";
import { Button, ButtonText } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Stack } from 'expo-router';
import { COLORS } from '../../../constants/colors';

// Safe function to format price
const formatPrice = (price) => {
  try {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2);
  } catch (error) {
    console.warn('Error formatting price:', error);
    return '0.00';
  }
};

// Safe function to get product image
const getProductImage = (product) => {
  try {
    if (!product.images) {
      return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
    }
    
    if (typeof product.images === 'string') {
      try {
        const parsedImages = JSON.parse(product.images);
        return Array.isArray(parsedImages) && parsedImages.length > 0 
          ? parsedImages[0] 
          : 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
      } catch (parseError) {
        return product.images.startsWith('http') 
          ? product.images 
          : 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
      }
    }
    
    if (Array.isArray(product.images) && product.images.length > 0) {
      return product.images[0];
    }
    
    return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
  } catch (error) {
    console.warn('Error getting product image:', error);
    return 'https://via.placeholder.com/400x300/f0f0f0/999999?text=No+Image';
  }
};

export default function ProductDetailPage() {

  const { id } = useLocalSearchParams();
  const [product, setProduct] = useState(null);

  const { user } = useUser();
  const { addToCart, getCartItem } = useCart();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    fetchProduct();
  }, [id]);

  const fetchProduct = async () => {
    try {
      const response = await fetch(`http://localhost:5001/api/products/${id}`);
      if (response.ok) {
        const productData = await response.json();
        setProduct(productData);
      } else {
        Alert.alert('Hata', 'Ürün bulunamadı');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      Alert.alert('Hata', 'Ürün yüklenirken bir hata oluştu');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = () => {
    addToCart(product, quantity);
    Alert.alert('Sepete Eklendi', `${product.name} sepete eklendi`);
  };

  const handleBuyNow = () => {
    addToCart(product, quantity);
    router.push('/cart');
  };

  const increaseQuantity = () => {
    setQuantity(prev => prev + 1);
  };
  const handleWishlist = () => {
    Alert.alert('Favoriler', 'Favorilere eklendi');
  };

  const decreaseQuantity = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center">
          <Text className="text-gray-500">Ürün yükleniyor...</Text>
        </Box>
      </View>
    );
  }


  if (!product) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center">
          <Text className="text-gray-500">Ürün bulunamadı</Text>
        </Box>
      </View>
    );
  }

  const discountPercentage = product.original_price > product.price
    ? Math.round(((product.original_price - product.price) / product.original_price) * 100)
    : 0;

  const cartItem = getCartItem(product.id);

  return (
    <Box className='flex-1 items-center p-3'>
    <Stack.Screen options={{ title: product.name }} />

    <Card className="p-5 rounded-lg max-w-[960px] w-full flex-1">
        <Image
            source={{ uri: product.image, width: 300, height: 300 }}
            className="mb-6 h-[240px] w-full rounded-md aspect-[4/3]"
            alt={'${product.name}image'}
            resizeMode='contain'
        />
        <Text className="text-sm font-normal mb-2 text-typography-700">
            {product.name}
        </Text>
        <VStack className="mb-6">
            <Heading size="md" className="mb-4">
                {product.description}
            </Heading>
            <Text className="text-sm font-normal text-typography-700">
                ${product.price}
            </Text>
        </VStack>
        <Box className="flex-col sm:flex-row">
            <Button className="px-4 py-2 mr-0 mb-3 sm:mr-3 sm:mb-0 sm:flex-1">
                <ButtonText size="sm">Add to cart</ButtonText>
            </Button>
            <Button
                variant="outline"
                className="px-4 py-2 border-outline-300 sm:flex-1"
            >
                <ButtonText size="sm" className="text-typography-600">
                    Wishlist
                </ButtonText>
            </Button>
        </Box>
    </Card>
</Box>

  );
}



const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: COLORS.background,
      padding: 20,
    },
    productImage: {
      width: '100%',
      height: 300,
      resizeMode: 'contain',
      marginBottom: 20,
    },
    productName: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 10,
    },
    productPrice: {
      fontSize: 18,
      fontWeight: 'bold',
      marginBottom: 10,
    },
    productDescription: {
      fontSize: 16,
      marginBottom: 10,
    },
    quantityContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 10,
    },
    quantityButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: '#f0f0f0',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: '#e0e0e0',
    },
    quantityText: {
      fontSize: 18,
      marginHorizontal: 10,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    actionButton: {
      flex: 1,
      paddingVertical: 10,
      paddingHorizontal: 20,
      borderRadius: 10,
      marginHorizontal: 5,
    },
    actionButtonText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: 'white',
      textAlign: 'center',
    },
  });


