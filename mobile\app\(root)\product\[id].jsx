import { useLocalSearchParams } from 'expo-router';
import { Card } from '../../components/ui/card';
import { Image } from '../../components/ui/image';
import { Box } from '../../components/ui/box';
import { Button } from '../../components/ui/button';
import { Heading } from '../../components/ui/heading';
import { VStack } from '../../components/ui/vstack';
import { ButtonText } from '../../components/ui/button';
import { Stack } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { API_URL } from '../../hooks/useProducts';
import { ActivityIndicator, View } from 'react-native';



// API'den tek ürün getiren function
async function fetchProduct(id) {
    try {
        // Önce tek ürün endpoint'ini dene
        const response = await fetch(`${API_URL}/products/${id}`);
        if (response.ok) {
            return response.json();
        }

        // Tek ürün endpoint'i yoksa, tüm ürünleri çek ve filtrele
        const allProductsResponse = await fetch(`${API_URL}/products`);
        if (!allProductsResponse.ok) {
            throw new Error('Ürünler yüklenemedi');
        }

        const allProducts = await allProductsResponse.json();
        const product = allProducts.find(p => p.id === parseInt(id));

        if (!product) {
            throw new Error('Ürün bulunamadı');
        }

        return product;
    } catch (error) {
        console.error('Error fetching product:', error);
        throw error;
    }
}

export default function ProductDetailsScreen() {
    const { id } = useLocalSearchParams();

    // TanStack Query ile ürün detayını çek
    const { data: product, isLoading, error } = useQuery({
        queryKey: ['product', id],
        queryFn: () => fetchProduct(id),
        enabled: !!id, // id varsa query'yi çalıştır
    });

    if (isLoading) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" />
                <Text style={{ marginTop: 10 }}>Ürün yükleniyor...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <Text style={{ color: 'red' }}>Ürün yüklenirken hata oluştu</Text>
                <Text style={{ color: 'red', fontSize: 12 }}>{error.message}</Text>
            </View>
        );
    }

    if (!product) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <Text>Ürün bulunamadı</Text>
            </View>
        );
    }

    // API'den gelen images array'inden ilk resmi al
    const imageUrl = product.images && product.images.length > 0
        ? product.images[0]
        : 'https://via.placeholder.com/300x300?text=No+Image';

    return (
        <Box className='flex-1 items-center p-3'>
            <Stack.Screen options={{ title: product.name }} />

            <Card className="p-5 rounded-lg max-w-[960px] w-full flex-1">
                <Image
                    source={{ uri: imageUrl, width: 300, height: 300 }}
                    className="mb-6 h-[240px] w-full rounded-md aspect-[4/3]"
                    alt={`${product.name} image`}
                    resizeMode='contain'
                />
                <Text className="text-sm font-normal mb-2 text-typography-700">
                    {product.name}
                </Text>
                <VStack className="mb-6">
                    <Heading size="md" className="mb-4">
                        {product.description}
                    </Heading>
                    <Text className="text-lg font-bold text-typography-700 mb-2">
                        ${product.price}
                    </Text>
                    {product.original_price && product.original_price !== product.price && (
                        <Text className="text-sm text-gray-500 line-through">
                            ${product.original_price}
                        </Text>
                    )}
                    {product.discount_percentage > 0 && (
                        <Text className="text-sm text-green-600">
                            %{product.discount_percentage} indirim
                        </Text>
                    )}
                    <Text className="text-sm text-typography-600 mt-2">
                        Stok: {product.stock} adet
                    </Text>
                    <Text className="text-sm text-typography-600">
                        Kategori: {product.category}
                    </Text>
                </VStack>
                <Box className="flex-col sm:flex-row">
                    <Button className="px-4 py-2 mr-0 mb-3 sm:mr-3 sm:mb-0 sm:flex-1">
                        <ButtonText size="sm">Sepete Ekle</ButtonText>
                    </Button>
                    <Button
                        variant="outline"
                        className="px-4 py-2 border-outline-300 sm:flex-1"
                    >
                        <ButtonText size="sm" className="text-typography-600">
                            Favorilere Ekle
                        </ButtonText>
                    </Button>
                </Box>
            </Card>
        </Box>
    );
}   