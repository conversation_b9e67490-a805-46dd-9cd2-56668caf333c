import { useLocalSearchParams } from 'expo-router';
import { Text } from '@/components/ui/text';
import { getProducts } from '@/hooks/useProducts';
import { Card } from '@/components/ui/card';
import { Image } from '@/components/ui/image';
import { Box } from '@/components/ui/box';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { VStack } from '@/components/ui/vstack';
import { ButtonText } from '@/components/ui/button';
import { Stack } from 'expo-router';



export default function ProductDetailsScreen() {


    const { id } = useLocalSearchParams();

    // TanStack Query ile ürün detayını çek
    const { data: product, isLoading, error } = useQuery({
        queryKey: ['product', id],
        queryFn: () => fetchProduct(id),
        enabled: !!id, // id varsa query'yi ç<PERSON>tır
    });

    if (isLoading) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" />
                <Text style={{ marginTop: 10 }}>Ürün yükleniyor...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <Text style={{ color: 'red' }}>Ürün yüklenirken hata oluştu</Text>
                <Text style={{ color: 'red', fontSize: 12 }}>{error.message}</Text>
            </View>
        );
    }

    if (!product) {
        return (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <Text>Ürün bulunamadı</Text>
            </View>
        );
    }
  

    return (

        <Box className='flex-1 items-center p-3'>
            <Stack.Screen options={{ title: product.name }} />

            <Card className="p-5 rounded-lg max-w-[960px] w-full flex-1">
                <Image
                    source={{ uri: product.image, width: 300, height: 300 }}
                    className="mb-6 h-[240px] w-full rounded-md aspect-[4/3]"
                    alt={'${product.name}image'}
                    resizeMode='contain'
                />
                <Text className="text-sm font-normal mb-2 text-typography-700">
                    {product.name}
                </Text>
                <VStack className="mb-6">
                    <Heading size="md" className="mb-4">
                        {product.description}
                    </Heading>
                    <Text className="text-sm font-normal text-typography-700">
                        ${product.price}
                    </Text>
                </VStack>
                <Box className="flex-col sm:flex-row">
                    <Button className="px-4 py-2 mr-0 mb-3 sm:mr-3 sm:mb-0 sm:flex-1">
                        <ButtonText size="sm">Add to cart</ButtonText>
                    </Button>
                    <Button
                        variant="outline"
                        className="px-4 py-2 border-outline-300 sm:flex-1"
                    >
                        <ButtonText size="sm" className="text-typography-600">
                            Wishlist
                        </ButtonText>
                    </Button>
                </Box>
            </Card>
        </Box>

    );
}   