import dotenv from 'dotenv';
import Seeder from '../seeders/seeder.js';

// Load environment variables
dotenv.config();

async function runSeeders() {
  try {
    console.log('Starting database seeding...');
    
    const seeder = new Seeder();
    await seeder.runSeeders();
    
    console.log('Database seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error running seeders:', error);
    process.exit(1);
  }
}

runSeeders();
