import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  TextInput, 
  ScrollView,
  AccessibilityInfo,
  Alert
} from 'react-native';
import { styled } from 'nativewind';

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);
const StyledScrollView = styled(ScrollView);

// Accessible Button Component
export const AccessibleButton = ({
  children,
  onPress,
  disabled = false,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  className = '',
  variant = 'primary',
  size = 'medium',
  ...props
}) => {
  const baseClasses = 'rounded-lg items-center justify-center';
  const variantClasses = {
    primary: 'bg-blue-500',
    secondary: 'bg-gray-500',
    danger: 'bg-red-500',
    success: 'bg-green-500',
  };
  const sizeClasses = {
    small: 'px-3 py-2',
    medium: 'px-4 py-3',
    large: 'px-6 py-4',
  };
  const disabledClasses = disabled ? 'opacity-50' : '';

  return (
    <StyledTouchableOpacity
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`}
      onPress={disabled ? undefined : onPress}
      disabled={disabled}
      accessible={true}
      accessibilityRole={accessibilityRole}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      accessibilityState={{ disabled }}
      {...props}
    >
      {typeof children === 'string' ? (
        <StyledText className="text-white font-medium">
          {children}
        </StyledText>
      ) : (
        children
      )}
    </StyledTouchableOpacity>
  );
};

// Accessible Text Input Component
export const AccessibleTextInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  keyboardType = 'default',
  accessibilityLabel,
  accessibilityHint,
  error,
  required = false,
  className = '',
  ...props
}) => {
  const inputId = React.useId();
  const errorId = `${inputId}-error`;

  return (
    <StyledView className={`mb-4 ${className}`}>
      {label && (
        <StyledText 
          className="text-gray-700 font-medium mb-2"
          accessibilityRole="text"
        >
          {label}
          {required && (
            <StyledText 
              className="text-red-500"
              accessibilityLabel="required"
            >
              {' *'}
            </StyledText>
          )}
        </StyledText>
      )}
      
      <StyledTextInput
        className={`border rounded-lg px-3 py-3 text-gray-900 ${
          error ? 'border-red-500' : 'border-gray-300'
        }`}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        secureTextEntry={secureTextEntry}
        keyboardType={keyboardType}
        accessible={true}
        accessibilityLabel={accessibilityLabel || label}
        accessibilityHint={accessibilityHint}
        accessibilityRequired={required}
        accessibilityInvalid={!!error}
        accessibilityDescribedBy={error ? errorId : undefined}
        {...props}
      />
      
      {error && (
        <StyledText 
          className="text-red-500 text-sm mt-1"
          accessibilityRole="alert"
          accessibilityLiveRegion="polite"
          nativeID={errorId}
        >
          {error}
        </StyledText>
      )}
    </StyledView>
  );
};

// Accessible Card Component
export const AccessibleCard = ({
  children,
  onPress,
  accessibilityLabel,
  accessibilityHint,
  accessibilityRole = 'button',
  className = '',
  ...props
}) => {
  const Component = onPress ? StyledTouchableOpacity : StyledView;

  return (
    <Component
      className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}
      onPress={onPress}
      accessible={true}
      accessibilityRole={onPress ? accessibilityRole : 'text'}
      accessibilityLabel={accessibilityLabel}
      accessibilityHint={accessibilityHint}
      {...props}
    >
      {children}
    </Component>
  );
};

// Accessible List Item Component
export const AccessibleListItem = ({
  title,
  subtitle,
  onPress,
  accessibilityLabel,
  accessibilityHint,
  rightElement,
  leftElement,
  className = '',
  ...props
}) => {
  const combinedLabel = accessibilityLabel || 
    `${title}${subtitle ? `, ${subtitle}` : ''}`;

  return (
    <StyledTouchableOpacity
      className={`flex-row items-center py-3 px-4 border-b border-gray-100 ${className}`}
      onPress={onPress}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={combinedLabel}
      accessibilityHint={accessibilityHint}
      {...props}
    >
      {leftElement && (
        <StyledView className="mr-3" importantForAccessibility="no">
          {leftElement}
        </StyledView>
      )}
      
      <StyledView className="flex-1">
        <StyledText 
          className="text-gray-900 font-medium"
          importantForAccessibility="no"
        >
          {title}
        </StyledText>
        {subtitle && (
          <StyledText 
            className="text-gray-500 text-sm mt-1"
            importantForAccessibility="no"
          >
            {subtitle}
          </StyledText>
        )}
      </StyledView>
      
      {rightElement && (
        <StyledView className="ml-3" importantForAccessibility="no">
          {rightElement}
        </StyledView>
      )}
    </StyledTouchableOpacity>
  );
};

// Accessible Modal Component
export const AccessibleModal = ({
  visible,
  onClose,
  title,
  children,
  className = '',
  ...props
}) => {
  React.useEffect(() => {
    if (visible) {
      // Announce modal opening
      AccessibilityInfo.announceForAccessibility(`${title} dialog opened`);
    }
  }, [visible, title]);

  if (!visible) return null;

  return (
    <StyledView 
      className="absolute inset-0 bg-black/50 justify-center items-center z-50"
      accessible={true}
      accessibilityRole="dialog"
      accessibilityLabel={title}
      accessibilityModal={true}
    >
      <StyledView 
        className={`bg-white rounded-lg p-6 m-4 max-w-sm w-full ${className}`}
        accessible={false}
      >
        {title && (
          <StyledText 
            className="text-xl font-bold mb-4"
            accessibilityRole="header"
          >
            {title}
          </StyledText>
        )}
        
        {children}
        
        <AccessibleButton
          onPress={onClose}
          variant="secondary"
          className="mt-4"
          accessibilityLabel="Close dialog"
          accessibilityHint="Closes the current dialog"
        >
          Close
        </AccessibleButton>
      </StyledView>
    </StyledView>
  );
};

// Accessible Tab Component
export const AccessibleTabs = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
  ...props
}) => {
  return (
    <StyledView 
      className={`flex-row border-b border-gray-200 ${className}`}
      accessibilityRole="tablist"
      {...props}
    >
      {tabs.map((tab, index) => (
        <StyledTouchableOpacity
          key={tab.key || index}
          className={`flex-1 py-3 px-4 border-b-2 ${
            activeTab === tab.key 
              ? 'border-blue-500' 
              : 'border-transparent'
          }`}
          onPress={() => onTabChange(tab.key)}
          accessible={true}
          accessibilityRole="tab"
          accessibilityLabel={tab.label}
          accessibilityState={{ selected: activeTab === tab.key }}
          accessibilityHint={`Switches to ${tab.label} tab`}
        >
          <StyledText 
            className={`text-center font-medium ${
              activeTab === tab.key 
                ? 'text-blue-500' 
                : 'text-gray-500'
            }`}
            importantForAccessibility="no"
          >
            {tab.label}
          </StyledText>
        </StyledTouchableOpacity>
      ))}
    </StyledView>
  );
};

// Accessible Loading Component
export const AccessibleLoading = ({
  text = 'Loading...',
  className = '',
  ...props
}) => {
  React.useEffect(() => {
    AccessibilityInfo.announceForAccessibility(text);
  }, [text]);

  return (
    <StyledView 
      className={`justify-center items-center p-4 ${className}`}
      accessible={true}
      accessibilityRole="progressbar"
      accessibilityLabel={text}
      accessibilityLiveRegion="polite"
      {...props}
    >
      <StyledText className="text-gray-600">
        {text}
      </StyledText>
    </StyledView>
  );
};

// Accessibility Helper Hook
export const useAccessibility = () => {
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = React.useState(false);
  const [isReduceMotionEnabled, setIsReduceMotionEnabled] = React.useState(false);

  React.useEffect(() => {
    // Check if screen reader is enabled
    AccessibilityInfo.isScreenReaderEnabled().then(setIsScreenReaderEnabled);
    
    // Check if reduce motion is enabled
    AccessibilityInfo.isReduceMotionEnabled().then(setIsReduceMotionEnabled);

    // Listen for changes
    const screenReaderListener = AccessibilityInfo.addEventListener(
      'screenReaderChanged',
      setIsScreenReaderEnabled
    );

    const reduceMotionListener = AccessibilityInfo.addEventListener(
      'reduceMotionChanged',
      setIsReduceMotionEnabled
    );

    return () => {
      screenReaderListener?.remove();
      reduceMotionListener?.remove();
    };
  }, []);

  const announceForAccessibility = (message) => {
    AccessibilityInfo.announceForAccessibility(message);
  };

  const setAccessibilityFocus = (reactTag) => {
    AccessibilityInfo.setAccessibilityFocus(reactTag);
  };

  return {
    isScreenReaderEnabled,
    isReduceMotionEnabled,
    announceForAccessibility,
    setAccessibilityFocus,
  };
};
