name: Backend CI/CD

on:
  push:
    branches: [main, develop]
    paths: ["backend/**"]
  pull_request:
    branches: [main]
    paths: ["backend/**"]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: wallet_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: |
          cd backend
          npm ci

      - name: Create test environment file
        run: |
          cd backend
          cat > .env.test << EOF
          NODE_ENV=test
          DATABASE_URL=postgresql://postgres:postgres@localhost:5432/wallet_test
          JWT_SECRET=test-jwt-secret
          UPSTASH_REDIS_REST_URL=redis://localhost:6379
          UPSTASH_REDIS_REST_TOKEN=test-token
          PORT=5001
          EOF

      - name: Run linting
        run: |
          cd backend
          npm run lint || echo "Linting not configured"

      - name: Run unit tests
        run: |
          cd backend
          npm run test:unit

      - name: Run integration tests
        run: |
          cd backend
          npm run test:integration

      - name: Run all tests with coverage
        run: |
          cd backend
          npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

      - name: Build application
        run: |
          cd backend
          npm run build || echo "Build script not configured"

  security:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run security audit
        run: |
          cd backend
          npm audit --audit-level moderate

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
          command: test

  deploy-staging:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: |
            ${{ secrets.DOCKER_REGISTRY }}/wallet-backend:staging-${{ github.sha }}
            ${{ secrets.DOCKER_REGISTRY }}/wallet-backend:staging-latest

      - name: Deploy to staging
        run: |
          echo "Deploying to staging environment"
          chmod +x scripts/deploy.sh
          ./scripts/deploy.sh staging ${{ github.sha }}

      - name: Run smoke tests
        run: |
          echo "Running smoke tests on staging"
          sleep 30
          curl -f ${{ secrets.STAGING_API_URL }}/api/health

      - name: Notify deployment
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#deployments"
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

  deploy-production:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ secrets.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: |
            ${{ secrets.DOCKER_REGISTRY }}/wallet-backend:${{ github.sha }}
            ${{ secrets.DOCKER_REGISTRY }}/wallet-backend:latest

      - name: Deploy to production
        run: |
          echo "Deploying to production environment"
          chmod +x scripts/deploy.sh
          ./scripts/deploy.sh production ${{ github.sha }}

      - name: Run smoke tests
        run: |
          echo "Running smoke tests on production"
          sleep 30
          curl -f ${{ secrets.PRODUCTION_API_URL }}/api/health

      - name: Notify deployment
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: "#deployments"
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
