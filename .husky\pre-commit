#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# Run lint-staged for staged files
npx lint-staged

# Run backend tests
echo "🧪 Running backend tests..."
cd backend
npm test -- --passWithNoTests --silent
if [ $? -ne 0 ]; then
  echo "❌ Backend tests failed. Commit aborted."
  exit 1
fi
cd ..

# Run mobile tests
echo "🧪 Running mobile tests..."
cd mobile
npm test -- --watchAll=false --passWithNoTests --silent
if [ $? -ne 0 ]; then
  echo "❌ Mobile tests failed. Commit aborted."
  exit 1
fi
cd ..

# Check for TODO/FIXME comments in staged files
echo "🔍 Checking for TODO/FIXME comments..."
staged_files=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|jsx|ts|tsx)$')
if [ -n "$staged_files" ]; then
  todo_count=$(echo "$staged_files" | xargs grep -l "TODO\|FIXME" | wc -l)
  if [ $todo_count -gt 0 ]; then
    echo "⚠️  Warning: Found TODO/FIXME comments in staged files:"
    echo "$staged_files" | xargs grep -n "TODO\|FIXME"
    echo "Consider resolving these before committing."
  fi
fi

# Check for console.log in production files
echo "🔍 Checking for console.log statements..."
if [ -n "$staged_files" ]; then
  console_count=$(echo "$staged_files" | xargs grep -l "console\.log" | wc -l)
  if [ $console_count -gt 0 ]; then
    echo "⚠️  Warning: Found console.log statements in staged files:"
    echo "$staged_files" | xargs grep -n "console\.log"
    echo "Consider removing these before committing to production."
  fi
fi

echo "✅ Pre-commit checks completed successfully!"
