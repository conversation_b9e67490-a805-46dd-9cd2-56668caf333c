import React from 'react';
import { render, act } from '@testing-library/react-native';
import { Text, TouchableOpacity } from 'react-native';
import { CartProvider, useCart } from '../../contexts/CartContext';

// Test component to interact with CartContext
const TestComponent = () => {
  const { cart, addToCart, removeFromCart, updateQuantity, clearCart, getTotalPrice } = useCart();
  
  const testProduct = {
    id: 1,
    name: 'Test Product',
    price: 99.99,
    image: 'test-image.jpg'
  };

  return (
    <>
      <Text testID="cart-count">{cart.length}</Text>
      <Text testID="total-price">{getTotalPrice()}</Text>
      <TouchableOpacity 
        testID="add-to-cart" 
        onPress={() => addToCart(testProduct, 2)}
      >
        <Text>Add to Cart</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        testID="remove-from-cart" 
        onPress={() => removeFromCart(1)}
      >
        <Text>Remove from Cart</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        testID="update-quantity" 
        onPress={() => updateQuantity(1, 5)}
      >
        <Text>Update Quantity</Text>
      </TouchableOpacity>
      <TouchableOpacity 
        testID="clear-cart" 
        onPress={clearCart}
      >
        <Text>Clear Cart</Text>
      </TouchableOpacity>
      {cart.map(item => (
        <Text key={item.id} testID={`cart-item-${item.id}`}>
          {item.name} - Quantity: {item.quantity}
        </Text>
      ))}
    </>
  );
};

describe('CartContext', () => {
  const renderWithProvider = () => {
    return render(
      <CartProvider>
        <TestComponent />
      </CartProvider>
    );
  };

  it('starts with empty cart', () => {
    const { getByTestId } = renderWithProvider();
    
    expect(getByTestId('cart-count').children[0]).toBe('0');
    expect(getByTestId('total-price').children[0]).toBe('0');
  });

  it('adds product to cart', async () => {
    const { getByTestId, fireEvent } = renderWithProvider();
    
    await act(async () => {
      fireEvent.press(getByTestId('add-to-cart'));
    });
    
    expect(getByTestId('cart-count').children[0]).toBe('1');
    expect(getByTestId('cart-item-1')).toBeTruthy();
    expect(getByTestId('cart-item-1').children[0]).toContain('Test Product - Quantity: 2');
  });

  it('calculates total price correctly', async () => {
    const { getByTestId, fireEvent } = renderWithProvider();
    
    await act(async () => {
      fireEvent.press(getByTestId('add-to-cart'));
    });
    
    // 99.99 * 2 = 199.98
    expect(getByTestId('total-price').children[0]).toBe('199.98');
  });

  it('updates product quantity', async () => {
    const { getByTestId, fireEvent } = renderWithProvider();
    
    // First add product
    await act(async () => {
      fireEvent.press(getByTestId('add-to-cart'));
    });
    
    // Then update quantity
    await act(async () => {
      fireEvent.press(getByTestId('update-quantity'));
    });
    
    expect(getByTestId('cart-item-1').children[0]).toContain('Test Product - Quantity: 5');
    // 99.99 * 5 = 499.95
    expect(getByTestId('total-price').children[0]).toBe('499.95');
  });

  it('removes product from cart', async () => {
    const { getByTestId, fireEvent, queryByTestId } = renderWithProvider();
    
    // First add product
    await act(async () => {
      fireEvent.press(getByTestId('add-to-cart'));
    });
    
    expect(getByTestId('cart-item-1')).toBeTruthy();
    
    // Then remove product
    await act(async () => {
      fireEvent.press(getByTestId('remove-from-cart'));
    });
    
    expect(queryByTestId('cart-item-1')).toBeNull();
    expect(getByTestId('cart-count').children[0]).toBe('0');
    expect(getByTestId('total-price').children[0]).toBe('0');
  });

  it('clears entire cart', async () => {
    const { getByTestId, fireEvent } = renderWithProvider();
    
    // Add product
    await act(async () => {
      fireEvent.press(getByTestId('add-to-cart'));
    });
    
    expect(getByTestId('cart-count').children[0]).toBe('1');
    
    // Clear cart
    await act(async () => {
      fireEvent.press(getByTestId('clear-cart'));
    });
    
    expect(getByTestId('cart-count').children[0]).toBe('0');
    expect(getByTestId('total-price').children[0]).toBe('0');
  });

  it('handles adding same product multiple times', async () => {
    const { getByTestId, fireEvent } = renderWithProvider();
    
    // Add same product twice
    await act(async () => {
      fireEvent.press(getByTestId('add-to-cart'));
      fireEvent.press(getByTestId('add-to-cart'));
    });
    
    // Should still be 1 item in cart but with increased quantity
    expect(getByTestId('cart-count').children[0]).toBe('1');
    expect(getByTestId('cart-item-1').children[0]).toContain('Test Product - Quantity: 4');
  });
});
