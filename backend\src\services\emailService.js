import nodemailer from 'nodemailer';
import { Resend } from 'resend';
import handlebars from 'handlebars';
import fs from 'fs';
import path from 'path';
import { AppError } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';

class EmailService {
  constructor() {
    this.transporter = null;
    this.resend = null;
    this.templatesPath = path.join(process.cwd(), 'src/templates/email');
    this.initializeService();
  }

  // Initialize email service based on configuration
  initializeService() {
    const emailProvider = process.env.EMAIL_PROVIDER || 'nodemailer';

    if (emailProvider === 'resend') {
      this.initializeResend();
    } else {
      this.initializeNodemailer();
    }
  }

  // Initialize Resend
  initializeResend() {
    if (!process.env.RESEND_API_KEY) {
      logger.warn('RESEND_API_KEY not found, email service disabled');
      return;
    }

    this.resend = new Resend(process.env.RESEND_API_KEY);
    logger.info('Email service initialized with Resend');
  }

  // Initialize Nodemailer
  initializeNodemailer() {
    const emailConfig = {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    };

    if (!emailConfig.auth.user || !emailConfig.auth.pass) {
      logger.warn('SMTP credentials not found, email service disabled');
      return;
    }

    this.transporter = nodemailer.createTransporter(emailConfig);
    logger.info('Email service initialized with Nodemailer');
  }

  // Load and compile email template
  async loadTemplate(templateName, data = {}) {
    try {
      const templatePath = path.join(this.templatesPath, `${templateName}.hbs`);
      
      if (!fs.existsSync(templatePath)) {
        throw new Error(`Template ${templateName} not found`);
      }

      const templateSource = fs.readFileSync(templatePath, 'utf8');
      const template = handlebars.compile(templateSource);
      
      return template(data);
    } catch (error) {
      logger.error(`Failed to load template ${templateName}:`, error);
      throw new AppError(`Failed to load email template`, 500);
    }
  }

  // Send email using Resend
  async sendWithResend(to, subject, html, attachments = []) {
    try {
      const emailData = {
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: Array.isArray(to) ? to : [to],
        subject,
        html,
      };

      if (attachments.length > 0) {
        emailData.attachments = attachments;
      }

      const result = await this.resend.emails.send(emailData);
      
      logger.info('Email sent via Resend:', {
        id: result.id,
        to: emailData.to,
        subject,
      });

      return result;
    } catch (error) {
      logger.error('Failed to send email via Resend:', error);
      throw new AppError('Failed to send email', 500);
    }
  }

  // Send email using Nodemailer
  async sendWithNodemailer(to, subject, html, attachments = []) {
    try {
      const mailOptions = {
        from: process.env.FROM_EMAIL || '<EMAIL>',
        to: Array.isArray(to) ? to.join(', ') : to,
        subject,
        html,
        attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      logger.info('Email sent via Nodemailer:', {
        messageId: result.messageId,
        to: mailOptions.to,
        subject,
      });

      return result;
    } catch (error) {
      logger.error('Failed to send email via Nodemailer:', error);
      throw new AppError('Failed to send email', 500);
    }
  }

  // Generic send email method
  async sendEmail(to, subject, templateName, data = {}, attachments = []) {
    try {
      if (!this.resend && !this.transporter) {
        logger.warn('Email service not configured, skipping email send');
        return null;
      }

      const html = await this.loadTemplate(templateName, data);

      if (this.resend) {
        return await this.sendWithResend(to, subject, html, attachments);
      } else {
        return await this.sendWithNodemailer(to, subject, html, attachments);
      }
    } catch (error) {
      logger.error('Failed to send email:', error);
      throw error;
    }
  }

  // Welcome email
  async sendWelcomeEmail(user) {
    const subject = 'Welcome to Wallet App!';
    const data = {
      name: user.name,
      email: user.email,
      appName: process.env.APP_NAME || 'Wallet App',
      loginUrl: `${process.env.FRONTEND_URL}/login`,
    };

    return await this.sendEmail(user.email, subject, 'welcome', data);
  }

  // Email verification
  async sendVerificationEmail(user, verificationToken) {
    const subject = 'Verify Your Email Address';
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${verificationToken}`;
    
    const data = {
      name: user.name,
      verificationUrl,
      appName: process.env.APP_NAME || 'Wallet App',
    };

    return await this.sendEmail(user.email, subject, 'email-verification', data);
  }

  // Password reset email
  async sendPasswordResetEmail(user, resetToken) {
    const subject = 'Reset Your Password';
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const data = {
      name: user.name,
      resetUrl,
      appName: process.env.APP_NAME || 'Wallet App',
      expiryTime: '1 hour',
    };

    return await this.sendEmail(user.email, subject, 'password-reset', data);
  }

  // Order confirmation email
  async sendOrderConfirmationEmail(user, order) {
    const subject = `Order Confirmation - #${order.id}`;
    
    const data = {
      name: user.name,
      order,
      appName: process.env.APP_NAME || 'Wallet App',
      orderUrl: `${process.env.FRONTEND_URL}/orders/${order.id}`,
    };

    return await this.sendEmail(user.email, subject, 'order-confirmation', data);
  }

  // Payment confirmation email
  async sendPaymentConfirmationEmail(user, payment) {
    const subject = 'Payment Confirmation';
    
    const data = {
      name: user.name,
      payment,
      appName: process.env.APP_NAME || 'Wallet App',
    };

    return await this.sendEmail(user.email, subject, 'payment-confirmation', data);
  }

  // Shipping notification email
  async sendShippingNotificationEmail(user, order, trackingInfo) {
    const subject = `Your Order Has Shipped - #${order.id}`;
    
    const data = {
      name: user.name,
      order,
      trackingInfo,
      appName: process.env.APP_NAME || 'Wallet App',
      trackingUrl: trackingInfo.trackingUrl,
    };

    return await this.sendEmail(user.email, subject, 'shipping-notification', data);
  }

  // Newsletter email
  async sendNewsletterEmail(subscribers, subject, content) {
    const data = {
      content,
      appName: process.env.APP_NAME || 'Wallet App',
      unsubscribeUrl: `${process.env.FRONTEND_URL}/unsubscribe`,
    };

    const promises = subscribers.map(subscriber => 
      this.sendEmail(subscriber.email, subject, 'newsletter', {
        ...data,
        name: subscriber.name,
        unsubscribeUrl: `${data.unsubscribeUrl}?token=${subscriber.unsubscribeToken}`,
      })
    );

    return await Promise.allSettled(promises);
  }

  // Promotional email
  async sendPromotionalEmail(user, promotion) {
    const subject = promotion.subject || 'Special Offer Just for You!';
    
    const data = {
      name: user.name,
      promotion,
      appName: process.env.APP_NAME || 'Wallet App',
      shopUrl: `${process.env.FRONTEND_URL}/products`,
    };

    return await this.sendEmail(user.email, subject, 'promotional', data);
  }

  // Send bulk emails
  async sendBulkEmails(emails) {
    const results = [];
    const batchSize = 10; // Process in batches to avoid rate limits

    for (let i = 0; i < emails.length; i += batchSize) {
      const batch = emails.slice(i, i + batchSize);
      const batchPromises = batch.map(email => 
        this.sendEmail(email.to, email.subject, email.template, email.data, email.attachments)
          .catch(error => ({ error, email }))
      );

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);

      // Add delay between batches
      if (i + batchSize < emails.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  // Test email configuration
  async testEmailConfiguration() {
    try {
      if (this.resend) {
        // Test Resend configuration
        const testEmail = {
          from: process.env.FROM_EMAIL || '<EMAIL>',
          to: [process.env.TEST_EMAIL || '<EMAIL>'],
          subject: 'Email Service Test',
          html: '<p>This is a test email to verify email service configuration.</p>',
        };

        await this.resend.emails.send(testEmail);
        logger.info('Email service test successful (Resend)');
        return true;
      } else if (this.transporter) {
        // Test Nodemailer configuration
        await this.transporter.verify();
        logger.info('Email service test successful (Nodemailer)');
        return true;
      } else {
        logger.warn('Email service not configured');
        return false;
      }
    } catch (error) {
      logger.error('Email service test failed:', error);
      return false;
    }
  }
}

export default new EmailService();
