import express from "express";
import { sql } from "../config/db.js";
import { protect, restrictTo } from "../middleware/auth.js";
import { validateProduct } from "../middleware/validation.js";
import { AppError } from "../middleware/errorHandler.js";

const router = express.Router();

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get all active products
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: Filter featured products
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of products to return
 *     responses:
 *       200:
 *         description: List of products
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Product'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', async (req, res, next) => {
    try {
        const { category, featured, limit } = req.query;

        let query = sql`SELECT * FROM products WHERE status = 'active'`;

        if (category) {
            query = sql`SELECT * FROM products WHERE status = 'active' AND category = ${category}`;
        }

        if (featured === 'true') {
            query = sql`SELECT * FROM products WHERE status = 'active' AND featured = true`;
        }

        query = sql`${query} ORDER BY created_at DESC`;

        if (limit) {
            query = sql`${query} LIMIT ${parseInt(limit)}`;
        }

        const products = await query;
        res.status(200).json(products);
    } catch (error) {
        next(new AppError('Error fetching products', 500));
    }
});

// Get products by user ID (seller's products)
router.get('/user/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const products = await sql`
            SELECT * FROM products 
            WHERE seller_id = ${userId} 
            ORDER BY created_at DESC
        `;
        res.status(200).json(products);
    } catch (error) {
        console.error('Error fetching user products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get product by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid product ID" });
        }

        const product = await sql`
            SELECT * FROM products WHERE id = ${id}
        `;
        
        if (product.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }
        
        res.status(200).json(product[0]);
    } catch (error) {
        console.error('Error fetching product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Create new product
router.post('/', async (req, res) => {
    try {
        const {
            name,
            description,
            price,
            original_price,
            discount_percentage = 0,
            images = [],
            category,
            stock = 0,
            featured = false,
            status = 'active',
            seller_id,
            specifications = {},
            tags = [],
            dynamic_pricing = {}
        } = req.body;

        // Validation
        if (!name || !description || !price || !category || !seller_id) {
            return res.status(400).json({ message: 'Missing required fields' });
        }

        const product = await sql`
            INSERT INTO products (
                name, description, price, original_price, discount_percentage,
                images, category, stock, featured, status, seller_id,
                specifications, tags, dynamic_pricing
            ) VALUES (
                ${name}, ${description}, ${price}, ${original_price || price}, ${discount_percentage},
                ${JSON.stringify(images)}, ${category}, ${stock}, ${featured}, ${status}, ${seller_id},
                ${JSON.stringify(specifications)}, ${JSON.stringify(tags)}, ${JSON.stringify(dynamic_pricing)}
            ) RETURNING *
        `;

        res.status(201).json(product[0]);
    } catch (error) {
        console.error('Error creating product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Update product
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid product ID" });
        }

        const {
            name,
            description,
            price,
            original_price,
            discount_percentage,
            images,
            category,
            stock,
            featured,
            status,
            specifications,
            tags,
            dynamic_pricing
        } = req.body;

        const result = await sql`
            UPDATE products SET
                name = ${name},
                description = ${description},
                price = ${price},
                original_price = ${original_price || price},
                discount_percentage = ${discount_percentage || 0},
                images = ${images ? JSON.stringify(images) : '[]'},
                category = ${category},
                stock = ${stock || 0},
                featured = ${featured || false},
                status = ${status || 'active'},
                specifications = ${specifications ? JSON.stringify(specifications) : '{}'},
                tags = ${tags ? JSON.stringify(tags) : '[]'},
                dynamic_pricing = ${dynamic_pricing ? JSON.stringify(dynamic_pricing) : '{}'},
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${id}
            RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json(result[0]);
    } catch (error) {
        console.error('Error updating product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Delete product
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        if (isNaN(parseInt(id))) {
            return res.status(400).json({ message: "Invalid product ID" });
        }

        const result = await sql`
            DELETE FROM products WHERE id = ${id} RETURNING *
        `;

        if (result.length === 0) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json({ message: "Product deleted successfully" });
    } catch (error) {
        console.error('Error deleting product:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Search products
router.get('/search/:query', async (req, res) => {
    try {
        const { query } = req.params;
        
        const products = await sql`
            SELECT * FROM products 
            WHERE (name ILIKE ${'%' + query + '%'} OR description ILIKE ${'%' + query + '%'})
            AND status = 'active'
            ORDER BY created_at DESC
        `;
        
        res.status(200).json(products);
    } catch (error) {
        console.error('Error searching products:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

// Get products by category
router.get('/category/:category', async (req, res) => {
    try {
        const { category } = req.params;
        
        const products = await sql`
            SELECT * FROM products 
            WHERE category = ${category} AND status = 'active'
            ORDER BY created_at DESC
        `;
        
        res.status(200).json(products);
    } catch (error) {
        console.error('Error fetching products by category:', error);
        res.status(500).json({ message: "Internal server error" });
    }
});

export default router;
