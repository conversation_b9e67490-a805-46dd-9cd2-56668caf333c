# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/wallet_db

# Server Configuration
PORT=5001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# Upstash Redis Configuration
UPSTASH_REDIS_REST_URL=your-upstash-redis-url
UPSTASH_REDIS_REST_TOKEN=your-upstash-redis-token

# API Configuration
API_URL=http://localhost:5001

# Sentry Configuration (Optional)
SENTRY_DSN=your-sentry-dsn-here

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (Optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
