version: "3.8"

services:
  # Backend API (Development)
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "5001:5001"
      - "9229:9229" # Debug port
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/wallet_dev
      - JWT_SECRET=dev-jwt-secret-change-in-production
      - UPSTASH_REDIS_REST_URL=redis://redis-dev:6379
      - UPSTASH_REDIS_REST_TOKEN=dev-token
      - PORT=5001
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081,http://localhost:19006
    depends_on:
      - postgres-dev
      - redis-dev
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    restart: unless-stopped
    command: npm run dev

  # PostgreSQL Database (Development)
  postgres-dev:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=wallet_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/scripts/init-dev.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  # Redis Cache (Development)
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped

  # Adminer (Database Management)
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres-dev
    restart: unless-stopped

  # Redis Commander (Redis Management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis-dev
    restart: unless-stopped

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  default:
    driver: bridge
