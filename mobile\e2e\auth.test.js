const { device, expect, element, by, waitFor } = require('detox');

describe('Authentication Flow', () => {
  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe('Sign In', () => {
    it('should show sign in screen on app launch', async () => {
      await waitForElementToBeVisible(by.text('Sign In'));
      await expect(element(by.id('email-input'))).toBeVisible();
      await expect(element(by.id('password-input'))).toBeVisible();
      await expect(element(by.id('sign-in-button'))).toBeVisible();
    });

    it('should sign in with valid credentials', async () => {
      await element(by.id('email-input')).typeText(testData.validUser.email);
      await element(by.id('password-input')).typeText(testData.validUser.password);
      await element(by.id('sign-in-button')).tap();

      // Should navigate to home screen
      await waitForElementToBeVisible(by.text('Welcome'));
      await expect(element(by.id('home-screen'))).toBeVisible();
    });

    it('should show error with invalid credentials', async () => {
      await element(by.id('email-input')).typeText(testData.invalidUser.email);
      await element(by.id('password-input')).typeText(testData.invalidUser.password);
      await element(by.id('sign-in-button')).tap();

      // Should show error message
      await waitForElementToBeVisible(by.text('Invalid credentials'));
    });

    it('should validate empty fields', async () => {
      await element(by.id('sign-in-button')).tap();

      // Should show validation errors
      await waitForElementToBeVisible(by.text('Email is required'));
      await waitForElementToBeVisible(by.text('Password is required'));
    });
  });

  describe('Sign Up', () => {
    beforeEach(async () => {
      await element(by.text('Sign Up')).tap();
      await waitForElementToBeVisible(by.text('Create Account'));
    });

    it('should show sign up form', async () => {
      await expect(element(by.id('name-input'))).toBeVisible();
      await expect(element(by.id('email-input'))).toBeVisible();
      await expect(element(by.id('password-input'))).toBeVisible();
      await expect(element(by.id('sign-up-button'))).toBeVisible();
    });

    it('should create account with valid data', async () => {
      await element(by.id('name-input')).typeText(testData.validUser.name);
      await element(by.id('email-input')).typeText(`new${Date.now()}@example.com`);
      await element(by.id('password-input')).typeText(testData.validUser.password);
      await element(by.id('sign-up-button')).tap();

      // Should show verification screen
      await waitForElementToBeVisible(by.text('Verify Email'));
    });

    it('should validate password strength', async () => {
      await element(by.id('name-input')).typeText(testData.validUser.name);
      await element(by.id('email-input')).typeText('<EMAIL>');
      await element(by.id('password-input')).typeText('123');
      await element(by.id('sign-up-button')).tap();

      // Should show password validation error
      await waitForElementToBeVisible(by.text('Password must be at least 6 characters'));
    });
  });

  describe('Sign Out', () => {
    beforeEach(async () => {
      // Sign in first
      await element(by.id('email-input')).typeText(testData.validUser.email);
      await element(by.id('password-input')).typeText(testData.validUser.password);
      await element(by.id('sign-in-button')).tap();
      await waitForElementToBeVisible(by.id('home-screen'));
    });

    it('should sign out successfully', async () => {
      await element(by.id('profile-button')).tap();
      await waitForElementToBeVisible(by.text('Profile'));
      
      await element(by.id('sign-out-button')).tap();
      
      // Should return to sign in screen
      await waitForElementToBeVisible(by.text('Sign In'));
      await expect(element(by.id('email-input'))).toBeVisible();
    });
  });
});
