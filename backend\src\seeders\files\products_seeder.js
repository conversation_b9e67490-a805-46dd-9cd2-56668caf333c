// Seeder: Create sample products
// Created: 2024-12-01T00:00:01.000Z

export const run = async (sql) => {
  console.log('Seeding products...');

  const products = [
    {
      name: 'iPhone 15 Pro',
      description: 'Latest iPhone with advanced camera system and A17 Pro chip',
      price: 999.99,
      original_price: 1099.99,
      discount_percentage: 9,
      images: JSON.stringify(['https://example.com/iphone15pro.jpg']),
      category: 'electronics',
      stock: 50,
      featured: true,
      seller_id: 'seller1',
      specifications: JSON.stringify({
        brand: 'Apple',
        model: 'iPhone 15 Pro',
        storage: '128GB',
        color: 'Natural Titanium'
      }),
      tags: ['smartphone', 'apple', 'premium']
    },
    {
      name: 'Samsung Galaxy S24',
      description: 'Flagship Android phone with AI features',
      price: 899.99,
      original_price: 999.99,
      discount_percentage: 10,
      images: JSON.stringify(['https://example.com/galaxys24.jpg']),
      category: 'electronics',
      stock: 30,
      featured: true,
      seller_id: 'seller1',
      specifications: JSON.stringify({
        brand: 'Samsung',
        model: 'Galaxy S24',
        storage: '256GB',
        color: 'Phantom Black'
      }),
      tags: ['smartphone', 'samsung', 'android']
    },
    {
      name: 'MacBook Air M3',
      description: 'Ultra-thin laptop with M3 chip for incredible performance',
      price: 1299.99,
      original_price: 1399.99,
      discount_percentage: 7,
      images: JSON.stringify(['https://example.com/macbookair.jpg']),
      category: 'electronics',
      stock: 25,
      featured: true,
      seller_id: 'seller2',
      specifications: JSON.stringify({
        brand: 'Apple',
        model: 'MacBook Air',
        processor: 'M3',
        ram: '8GB',
        storage: '256GB SSD'
      }),
      tags: ['laptop', 'apple', 'ultrabook']
    },
    {
      name: 'Sony WH-1000XM5',
      description: 'Premium noise-canceling wireless headphones',
      price: 349.99,
      original_price: 399.99,
      discount_percentage: 13,
      images: JSON.stringify(['https://example.com/sony-headphones.jpg']),
      category: 'electronics',
      stock: 75,
      featured: false,
      seller_id: 'seller2',
      specifications: JSON.stringify({
        brand: 'Sony',
        model: 'WH-1000XM5',
        type: 'Over-ear',
        connectivity: 'Bluetooth 5.2'
      }),
      tags: ['headphones', 'sony', 'wireless', 'noise-canceling']
    },
    {
      name: 'Nike Air Max 270',
      description: 'Comfortable running shoes with Max Air cushioning',
      price: 129.99,
      original_price: 149.99,
      discount_percentage: 13,
      images: JSON.stringify(['https://example.com/nike-airmax.jpg']),
      category: 'clothing',
      stock: 100,
      featured: false,
      seller_id: 'seller1',
      specifications: JSON.stringify({
        brand: 'Nike',
        model: 'Air Max 270',
        size: 'Various',
        color: 'Black/White'
      }),
      tags: ['shoes', 'nike', 'running', 'sports']
    }
  ];

  for (const product of products) {
    await sql`
      INSERT INTO products (
        name, description, price, original_price, discount_percentage,
        images, category, stock, featured, seller_id, specifications, tags
      )
      VALUES (
        ${product.name}, ${product.description}, ${product.price}, 
        ${product.original_price}, ${product.discount_percentage},
        ${product.images}, ${product.category}, ${product.stock}, 
        ${product.featured}, ${product.seller_id}, ${product.specifications}, 
        ${product.tags}
      )
      ON CONFLICT (name) DO NOTHING
    `;
  }

  console.log('Products seeded successfully');
};
