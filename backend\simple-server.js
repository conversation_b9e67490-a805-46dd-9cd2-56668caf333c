const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 5001;

// Middleware
app.use(cors());
app.use(express.json());

// Test endpoint
app.get('/', (req, res) => {
    res.json({ message: 'Backend server is running!' });
});

// Mock categories data
const mockCategories = [
    { id: 1, name: 'Elektronik', created_at: '2024-01-01' },
    { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', created_at: '2024-01-02' },
    { id: 3, name: '<PERSON><PERSON> & <PERSON>ş<PERSON>', created_at: '2024-01-03' }
];

// Mock products data
const mockProducts = [
    { 
        id: 1, 
        name: 'iPhone 15', 
        price: 25000, 
        category: 'Elektronik',
        stock: 10,
        created_at: '2024-01-01'
    },
    { 
        id: 2, 
        name: 'Samsung Galaxy S24', 
        price: 22000, 
        category: 'Elektronik',
        stock: 15,
        created_at: '2024-01-02'
    }
];

// Categories API
app.get('/api/categories', (req, res) => {
    console.log('Categories endpoint called');
    res.json(mockCategories);
});

app.post('/api/categories', (req, res) => {
    const { name } = req.body;
    const newCategory = {
        id: mockCategories.length + 1,
        name,
        created_at: new Date().toISOString()
    };
    mockCategories.push(newCategory);
    res.status(201).json(newCategory);
});

app.put('/api/categories/:id', (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    const categoryIndex = mockCategories.findIndex(cat => cat.id == id);
    
    if (categoryIndex === -1) {
        return res.status(404).json({ message: 'Category not found' });
    }
    
    mockCategories[categoryIndex].name = name;
    res.json(mockCategories[categoryIndex]);
});

app.delete('/api/categories/:id', (req, res) => {
    const { id } = req.params;
    const categoryIndex = mockCategories.findIndex(cat => cat.id == id);
    
    if (categoryIndex === -1) {
        return res.status(404).json({ message: 'Category not found' });
    }
    
    mockCategories.splice(categoryIndex, 1);
    res.json({ message: 'Category deleted successfully' });
});

// Products API
app.get('/api/products', (req, res) => {
    console.log('Products endpoint called');
    res.json(mockProducts);
});

app.post('/api/products', (req, res) => {
    const { name, price, category, stock } = req.body;
    const newProduct = {
        id: mockProducts.length + 1,
        name,
        price: parseFloat(price),
        category,
        stock: parseInt(stock),
        created_at: new Date().toISOString()
    };
    mockProducts.push(newProduct);
    res.status(201).json(newProduct);
});

app.put('/api/products/:id', (req, res) => {
    const { id } = req.params;
    const { name, price, category, stock } = req.body;
    const productIndex = mockProducts.findIndex(prod => prod.id == id);
    
    if (productIndex === -1) {
        return res.status(404).json({ message: 'Product not found' });
    }
    
    mockProducts[productIndex] = {
        ...mockProducts[productIndex],
        name,
        price: parseFloat(price),
        category,
        stock: parseInt(stock)
    };
    res.json(mockProducts[productIndex]);
});

app.delete('/api/products/:id', (req, res) => {
    const { id } = req.params;
    const productIndex = mockProducts.findIndex(prod => prod.id == id);
    
    if (productIndex === -1) {
        return res.status(404).json({ message: 'Product not found' });
    }
    
    mockProducts.splice(productIndex, 1);
    res.json({ message: 'Product deleted successfully' });
});

// Users API (mock)
const mockUsers = [
    { id: 1, name: 'Admin User', email: '<EMAIL>', role: 'admin', created_at: '2024-01-01' }
];

app.get('/api/users', (req, res) => {
    console.log('Users endpoint called');
    res.json(mockUsers);
});

app.post('/api/users', (req, res) => {
    const { name, email, role } = req.body;
    const newUser = {
        id: mockUsers.length + 1,
        name,
        email,
        role: role || 'user',
        created_at: new Date().toISOString()
    };
    mockUsers.push(newUser);
    res.status(201).json(newUser);
});

app.put('/api/users/:id', (req, res) => {
    const { id } = req.params;
    const { name, email, role } = req.body;
    const userIndex = mockUsers.findIndex(user => user.id == id);
    
    if (userIndex === -1) {
        return res.status(404).json({ message: 'User not found' });
    }
    
    mockUsers[userIndex] = {
        ...mockUsers[userIndex],
        name,
        email,
        role
    };
    res.json(mockUsers[userIndex]);
});

app.delete('/api/users/:id', (req, res) => {
    const { id } = req.params;
    const userIndex = mockUsers.findIndex(user => user.id == id);
    
    if (userIndex === -1) {
        return res.status(404).json({ message: 'User not found' });
    }
    
    mockUsers.splice(userIndex, 1);
    res.json({ message: 'User deleted successfully' });
});

app.listen(PORT, () => {
    console.log(`🚀 Backend server running on http://localhost:${PORT}`);
    console.log(`📊 API endpoints available:`);
    console.log(`   GET  /api/categories`);
    console.log(`   POST /api/categories`);
    console.log(`   GET  /api/products`);
    console.log(`   POST /api/products`);
    console.log(`   GET  /api/users`);
    console.log(`   POST /api/users`);
});
