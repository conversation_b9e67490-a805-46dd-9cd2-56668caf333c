import fs from 'fs';
import path from 'path';
import { sql } from '../config/db.js';
import logger from '../config/logger.js';

class Seeder {
  constructor() {
    this.seedersPath = path.join(process.cwd(), 'src/seeders/files');
    this.seedTable = 'seed_history';
  }

  async init() {
    // Create seed history table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS ${sql(this.seedTable)} (
        id SERIAL PRIMARY KEY,
        seeder_name VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    logger.info('Seed history table initialized');
  }

  async getExecutedSeeders() {
    const result = await sql`
      SELECT seeder_name FROM ${sql(this.seedTable)} 
      ORDER BY seeder_name ASC
    `;
    return result.map(row => row.seeder_name);
  }

  async getPendingSeeders() {
    const executedSeeders = await this.getExecutedSeeders();
    const allSeeders = this.getAllSeederFiles();
    
    return allSeeders.filter(seeder => 
      !executedSeeders.includes(seeder.name)
    );
  }

  getAllSeederFiles() {
    if (!fs.existsSync(this.seedersPath)) {
      fs.mkdirSync(this.seedersPath, { recursive: true });
      return [];
    }

    const files = fs.readdirSync(this.seedersPath)
      .filter(file => file.endsWith('.js'))
      .sort();

    return files.map(file => ({
      name: file.replace('.js', ''),
      filename: file
    }));
  }

  async runSeeders(force = false) {
    await this.init();
    
    let seedersToRun;
    if (force) {
      seedersToRun = this.getAllSeederFiles();
      logger.info('Running all seeders (force mode)');
    } else {
      seedersToRun = await this.getPendingSeeders();
      if (seedersToRun.length === 0) {
        logger.info('No pending seeders');
        return;
      }
      logger.info(`Running ${seedersToRun.length} pending seeders`);
    }

    for (const seeder of seedersToRun) {
      await this.runSingleSeeder(seeder, force);
    }

    logger.info('All seeders completed successfully');
  }

  async runSingleSeeder(seeder, force = false) {
    const seederPath = path.join(this.seedersPath, seeder.filename);
    
    try {
      logger.info(`Running seeder: ${seeder.name}`);
      
      const seederModule = await import(seederPath);
      const { run } = seederModule.default || seederModule;

      if (typeof run !== 'function') {
        throw new Error(`Seeder ${seeder.filename} does not export a 'run' function`);
      }

      // Run the seeder
      await run(sql);

      // Record the seeder as executed (only if not force mode)
      if (!force) {
        await sql`
          INSERT INTO ${sql(this.seedTable)} (seeder_name)
          VALUES (${seeder.name})
          ON CONFLICT (seeder_name) DO NOTHING
        `;
      }

      logger.info(`Seeder completed: ${seeder.name}`);
    } catch (error) {
      logger.error(`Seeder failed: ${seeder.name}`, error);
      throw error;
    }
  }

  async resetSeeders() {
    await sql`DELETE FROM ${sql(this.seedTable)}`;
    logger.info('Seed history cleared');
  }

  async createSeeder(name) {
    const filename = `${name.replace(/\s+/g, '_').toLowerCase()}.js`;
    const filepath = path.join(this.seedersPath, filename);

    const template = `// Seeder: ${name}
// Created: ${new Date().toISOString()}

export const run = async (sql) => {
  // Add your seeding logic here
  // Example:
  // await sql\`
  //   INSERT INTO users (name, email, username, password, role)
  //   VALUES 
  //     ('Admin User', '<EMAIL>', 'admin', 'hashed_password', 'admin'),
  //     ('Test User', '<EMAIL>', 'testuser', 'hashed_password', 'user')
  //   ON CONFLICT (email) DO NOTHING
  // \`;
  
  console.log('Seeder ${name} executed');
};
`;

    if (!fs.existsSync(this.seedersPath)) {
      fs.mkdirSync(this.seedersPath, { recursive: true });
    }

    fs.writeFileSync(filepath, template);
    logger.info(`Seeder created: ${filename}`);
    return filename;
  }
}

export default Seeder;
