import { renderHook, waitFor } from '@testing-library/react-native';
import { useProducts } from '../../hooks/useProducts';

// Mock fetch
global.fetch = jest.fn();

describe('useProducts Hook', () => {
  beforeEach(() => {
    fetch.mockClear();
  });

  it('should fetch products successfully', async () => {
    const mockProducts = [
      { id: 1, name: 'Product 1', price: 99.99 },
      { id: 2, name: 'Product 2', price: 149.99 }
    ];

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProducts,
    });

    const { result } = renderHook(() => useProducts());

    expect(result.current.loading).toBe(true);
    expect(result.current.products).toEqual([]);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.products).toEqual(mockProducts);
    expect(result.current.error).toBe(null);
  });

  it('should handle fetch error', async () => {
    fetch.mockRejectedValueOnce(new Error('Network error'));

    const { result } = renderHook(() => useProducts());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.products).toEqual([]);
    expect(result.current.error).toBe('Failed to fetch products');
  });

  it('should handle HTTP error response', async () => {
    fetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      json: async () => ({ message: 'Server error' }),
    });

    const { result } = renderHook(() => useProducts());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.products).toEqual([]);
    expect(result.current.error).toBe('Failed to fetch products');
  });

  it('should refetch products when refetch is called', async () => {
    const mockProducts = [
      { id: 1, name: 'Product 1', price: 99.99 }
    ];

    fetch.mockResolvedValue({
      ok: true,
      json: async () => mockProducts,
    });

    const { result } = renderHook(() => useProducts());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous calls
    fetch.mockClear();

    // Call refetch
    result.current.refetch();

    expect(fetch).toHaveBeenCalledTimes(1);
  });

  it('should filter products by category', async () => {
    const mockProducts = [
      { id: 1, name: 'Phone', category: 'electronics', price: 99.99 },
      { id: 2, name: 'Shirt', category: 'clothing', price: 29.99 }
    ];

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProducts,
    });

    const { result } = renderHook(() => useProducts('electronics'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('category=electronics')
    );
  });
});
