const { device, expect, element, by, waitFor } = require('detox');

describe('Shopping Flow', () => {
  beforeAll(async () => {
    // Sign in before running shopping tests
    await element(by.id('email-input')).typeText(testData.validUser.email);
    await element(by.id('password-input')).typeText(testData.validUser.password);
    await element(by.id('sign-in-button')).tap();
    await waitForElementToBeVisible(by.id('home-screen'));
  });

  describe('Product Browsing', () => {
    it('should display products on home screen', async () => {
      await waitForElementToBeVisible(by.id('products-list'));
      await expect(element(by.id('product-card').atIndex(0))).toBeVisible();
    });

    it('should filter products by category', async () => {
      await element(by.id('category-electronics')).tap();
      await waitForElementToBeVisible(by.text('Electronics'));
      
      // Should show filtered products
      await waitForElementToBeVisible(by.id('products-list'));
    });

    it('should search for products', async () => {
      await element(by.id('search-input')).typeText('iPhone');
      await element(by.id('search-button')).tap();
      
      // Should show search results
      await waitForElementToBeVisible(by.text('Search Results'));
      await waitForElementToBeVisible(by.id('products-list'));
    });

    it('should view product details', async () => {
      await element(by.id('product-card').atIndex(0)).tap();
      
      // Should navigate to product details
      await waitForElementToBeVisible(by.id('product-details-screen'));
      await expect(element(by.id('product-name'))).toBeVisible();
      await expect(element(by.id('product-price'))).toBeVisible();
      await expect(element(by.id('product-description'))).toBeVisible();
      await expect(element(by.id('add-to-cart-button'))).toBeVisible();
    });
  });

  describe('Shopping Cart', () => {
    beforeEach(async () => {
      // Navigate to home and clear cart
      await element(by.id('home-tab')).tap();
      await element(by.id('cart-button')).tap();
      await waitForElementToBeVisible(by.id('cart-screen'));
      
      // Clear cart if not empty
      try {
        await element(by.id('clear-cart-button')).tap();
        await element(by.text('Yes')).tap();
      } catch (e) {
        // Cart already empty
      }
      
      await element(by.id('home-tab')).tap();
    });

    it('should add product to cart', async () => {
      await element(by.id('product-card').atIndex(0)).tap();
      await waitForElementToBeVisible(by.id('product-details-screen'));
      
      await element(by.id('add-to-cart-button')).tap();
      
      // Should show success message
      await waitForElementToBeVisible(by.text('Added to cart'));
      
      // Cart badge should update
      await expect(element(by.id('cart-badge'))).toHaveText('1');
    });

    it('should view cart contents', async () => {
      // Add a product first
      await element(by.id('product-card').atIndex(0)).tap();
      await element(by.id('add-to-cart-button')).tap();
      await device.pressBack();
      
      await element(by.id('cart-button')).tap();
      await waitForElementToBeVisible(by.id('cart-screen'));
      
      // Should show cart items
      await expect(element(by.id('cart-item').atIndex(0))).toBeVisible();
      await expect(element(by.id('cart-total'))).toBeVisible();
    });

    it('should update product quantity in cart', async () => {
      // Add a product first
      await element(by.id('product-card').atIndex(0)).tap();
      await element(by.id('add-to-cart-button')).tap();
      await device.pressBack();
      
      await element(by.id('cart-button')).tap();
      await waitForElementToBeVisible(by.id('cart-screen'));
      
      // Increase quantity
      await element(by.id('increase-quantity-button').atIndex(0)).tap();
      
      // Should update quantity and total
      await expect(element(by.id('quantity-text').atIndex(0))).toHaveText('2');
    });

    it('should remove product from cart', async () => {
      // Add a product first
      await element(by.id('product-card').atIndex(0)).tap();
      await element(by.id('add-to-cart-button')).tap();
      await device.pressBack();
      
      await element(by.id('cart-button')).tap();
      await waitForElementToBeVisible(by.id('cart-screen'));
      
      // Remove item
      await element(by.id('remove-item-button').atIndex(0)).tap();
      await element(by.text('Yes')).tap();
      
      // Should show empty cart
      await waitForElementToBeVisible(by.text('Your cart is empty'));
    });
  });

  describe('Wishlist', () => {
    it('should add product to wishlist', async () => {
      await element(by.id('product-card').atIndex(0)).tap();
      await waitForElementToBeVisible(by.id('product-details-screen'));
      
      await element(by.id('add-to-wishlist-button')).tap();
      
      // Should show success message
      await waitForElementToBeVisible(by.text('Added to wishlist'));
    });

    it('should view wishlist', async () => {
      await element(by.id('wishlist-tab')).tap();
      await waitForElementToBeVisible(by.id('wishlist-screen'));
      
      // Should show wishlist items
      await expect(element(by.id('wishlist-item').atIndex(0))).toBeVisible();
    });

    it('should remove product from wishlist', async () => {
      await element(by.id('wishlist-tab')).tap();
      await waitForElementToBeVisible(by.id('wishlist-screen'));
      
      await element(by.id('remove-from-wishlist-button').atIndex(0)).tap();
      
      // Should remove item
      await waitFor(element(by.id('wishlist-item').atIndex(0)))
        .not.toBeVisible()
        .withTimeout(5000);
    });
  });

  describe('Checkout Process', () => {
    beforeEach(async () => {
      // Add a product to cart
      await element(by.id('home-tab')).tap();
      await element(by.id('product-card').atIndex(0)).tap();
      await element(by.id('add-to-cart-button')).tap();
      await device.pressBack();
    });

    it('should proceed to checkout', async () => {
      await element(by.id('cart-button')).tap();
      await waitForElementToBeVisible(by.id('cart-screen'));
      
      await element(by.id('checkout-button')).tap();
      
      // Should navigate to checkout
      await waitForElementToBeVisible(by.id('checkout-screen'));
      await expect(element(by.id('shipping-address-section'))).toBeVisible();
      await expect(element(by.id('payment-method-section'))).toBeVisible();
    });

    it('should complete order', async () => {
      await element(by.id('cart-button')).tap();
      await element(by.id('checkout-button')).tap();
      await waitForElementToBeVisible(by.id('checkout-screen'));
      
      // Fill shipping address
      await element(by.id('address-input')).typeText('123 Test Street');
      await element(by.id('city-input')).typeText('Test City');
      await element(by.id('zip-input')).typeText('12345');
      
      // Select payment method
      await element(by.id('payment-method-card')).tap();
      
      // Place order
      await element(by.id('place-order-button')).tap();
      
      // Should show order confirmation
      await waitForElementToBeVisible(by.text('Order Placed Successfully'));
    });
  });
});
