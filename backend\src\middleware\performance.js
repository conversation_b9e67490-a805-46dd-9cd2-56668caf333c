import logger from '../config/logger.js';

// Performance monitoring middleware
const performanceMonitor = (req, res, next) => {
  const startTime = Date.now();
  const startUsage = process.cpuUsage();
  const startMemory = process.memoryUsage();

  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    const endUsage = process.cpuUsage(startUsage);
    const endMemory = process.memoryUsage();

    // Log performance metrics
    logger.info('Request Performance', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userCpuTime: `${endUsage.user / 1000}ms`,
      systemCpuTime: `${endUsage.system / 1000}ms`,
      memoryUsed: `${(endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024}MB`,
      totalMemory: `${endMemory.heapUsed / 1024 / 1024}MB`,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    // Set performance headers
    res.set('X-Response-Time', `${responseTime}ms`);
    res.set('X-Memory-Usage', `${endMemory.heapUsed / 1024 / 1024}MB`);

    // Call original end method
    originalEnd.apply(this, args);
  };

  next();
};

// Database query performance tracker
class QueryPerformanceTracker {
  constructor() {
    this.queries = new Map();
  }

  startQuery(queryId, query) {
    this.queries.set(queryId, {
      query,
      startTime: Date.now(),
      startMemory: process.memoryUsage().heapUsed
    });
  }

  endQuery(queryId) {
    const queryData = this.queries.get(queryId);
    if (!queryData) return;

    const endTime = Date.now();
    const endMemory = process.memoryUsage().heapUsed;
    const duration = endTime - queryData.startTime;
    const memoryDiff = endMemory - queryData.startMemory;

    logger.info('Database Query Performance', {
      query: queryData.query.substring(0, 100) + '...',
      duration: `${duration}ms`,
      memoryImpact: `${memoryDiff / 1024}KB`
    });

    this.queries.delete(queryId);

    // Alert for slow queries
    if (duration > 1000) {
      logger.warn('Slow Query Detected', {
        query: queryData.query,
        duration: `${duration}ms`
      });
    }
  }
}

// API endpoint performance metrics
const endpointMetrics = new Map();

const trackEndpointPerformance = (req, res, next) => {
  const endpoint = `${req.method} ${req.route?.path || req.path}`;
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    if (!endpointMetrics.has(endpoint)) {
      endpointMetrics.set(endpoint, {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        errors: 0
      });
    }

    const metrics = endpointMetrics.get(endpoint);
    metrics.count++;
    metrics.totalTime += duration;
    metrics.minTime = Math.min(metrics.minTime, duration);
    metrics.maxTime = Math.max(metrics.maxTime, duration);
    
    if (res.statusCode >= 400) {
      metrics.errors++;
    }

    // Log metrics every 100 requests
    if (metrics.count % 100 === 0) {
      logger.info('Endpoint Performance Summary', {
        endpoint,
        totalRequests: metrics.count,
        averageTime: `${(metrics.totalTime / metrics.count).toFixed(2)}ms`,
        minTime: `${metrics.minTime}ms`,
        maxTime: `${metrics.maxTime}ms`,
        errorRate: `${((metrics.errors / metrics.count) * 100).toFixed(2)}%`
      });
    }
  });

  next();
};

// Memory usage monitoring
const monitorMemoryUsage = () => {
  setInterval(() => {
    const usage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    logger.info('System Performance', {
      heapUsed: `${(usage.heapUsed / 1024 / 1024).toFixed(2)}MB`,
      heapTotal: `${(usage.heapTotal / 1024 / 1024).toFixed(2)}MB`,
      external: `${(usage.external / 1024 / 1024).toFixed(2)}MB`,
      rss: `${(usage.rss / 1024 / 1024).toFixed(2)}MB`,
      userCpuTime: `${(cpuUsage.user / 1000).toFixed(2)}ms`,
      systemCpuTime: `${(cpuUsage.system / 1000).toFixed(2)}ms`,
      uptime: `${process.uptime().toFixed(2)}s`
    });

    // Alert for high memory usage
    const heapUsedMB = usage.heapUsed / 1024 / 1024;
    if (heapUsedMB > 500) {
      logger.warn('High Memory Usage Detected', {
        heapUsed: `${heapUsedMB.toFixed(2)}MB`,
        threshold: '500MB'
      });
    }
  }, 60000); // Every minute
};

// Prometheus metrics endpoint
const getMetricsForPrometheus = () => {
  const usage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  let metrics = '';
  
  // Memory metrics
  metrics += `# HELP nodejs_heap_used_bytes Node.js heap used in bytes\n`;
  metrics += `# TYPE nodejs_heap_used_bytes gauge\n`;
  metrics += `nodejs_heap_used_bytes ${usage.heapUsed}\n`;
  
  metrics += `# HELP nodejs_heap_total_bytes Node.js heap total in bytes\n`;
  metrics += `# TYPE nodejs_heap_total_bytes gauge\n`;
  metrics += `nodejs_heap_total_bytes ${usage.heapTotal}\n`;
  
  // CPU metrics
  metrics += `# HELP nodejs_cpu_user_seconds_total Node.js user CPU time in seconds\n`;
  metrics += `# TYPE nodejs_cpu_user_seconds_total counter\n`;
  metrics += `nodejs_cpu_user_seconds_total ${cpuUsage.user / 1000000}\n`;
  
  metrics += `# HELP nodejs_cpu_system_seconds_total Node.js system CPU time in seconds\n`;
  metrics += `# TYPE nodejs_cpu_system_seconds_total counter\n`;
  metrics += `nodejs_cpu_system_seconds_total ${cpuUsage.system / 1000000}\n`;
  
  // Endpoint metrics
  for (const [endpoint, data] of endpointMetrics) {
    const sanitizedEndpoint = endpoint.replace(/[^a-zA-Z0-9_]/g, '_');
    
    metrics += `# HELP http_requests_total Total number of HTTP requests\n`;
    metrics += `# TYPE http_requests_total counter\n`;
    metrics += `http_requests_total{endpoint="${endpoint}"} ${data.count}\n`;
    
    metrics += `# HELP http_request_duration_ms HTTP request duration in milliseconds\n`;
    metrics += `# TYPE http_request_duration_ms histogram\n`;
    metrics += `http_request_duration_ms_sum{endpoint="${endpoint}"} ${data.totalTime}\n`;
    metrics += `http_request_duration_ms_count{endpoint="${endpoint}"} ${data.count}\n`;
  }
  
  return metrics;
};

export {
  performanceMonitor,
  QueryPerformanceTracker,
  trackEndpointPerformance,
  monitorMemoryUsage,
  getMetricsForPrometheus
};
