import { sql } from '../config/db.js';
import { cacheManager } from '../middleware/cache.js';
import logger from '../config/logger.js';

class SearchService {
  constructor() {
    this.searchCache = new Map();
    this.popularSearches = new Map();
    this.searchAnalytics = new Map();
  }

  // Full-text search for products
  async searchProducts(query, filters = {}, options = {}) {
    try {
      const {
        category,
        minPrice,
        maxPrice,
        inStock = true,
        featured,
        rating,
        sortBy = 'relevance',
        sortOrder = 'DESC',
        limit = 20,
        offset = 0
      } = filters;

      const { userId } = options;

      // Build search conditions
      let whereConditions = [`status = 'active'`];
      let params = [query];
      let paramIndex = 1;

      // Add filters
      if (category) {
        whereConditions.push(`category = $${++paramIndex}`);
        params.push(category);
      }

      if (minPrice) {
        whereConditions.push(`price >= $${++paramIndex}`);
        params.push(minPrice);
      }

      if (maxPrice) {
        whereConditions.push(`price <= $${++paramIndex}`);
        params.push(maxPrice);
      }

      if (inStock) {
        whereConditions.push(`stock > 0`);
      }

      if (featured !== undefined) {
        whereConditions.push(`featured = $${++paramIndex}`);
        params.push(featured);
      }

      if (rating) {
        whereConditions.push(`rating >= $${++paramIndex}`);
        params.push(rating);
      }

      const whereClause = whereConditions.join(' AND ');

      // Build sort clause
      let orderClause = 'ORDER BY ';
      switch (sortBy) {
        case 'price':
          orderClause += `price ${sortOrder}`;
          break;
        case 'rating':
          orderClause += `rating ${sortOrder}, review_count DESC`;
          break;
        case 'newest':
          orderClause += `created_at ${sortOrder}`;
          break;
        case 'popular':
          orderClause += `sales_count ${sortOrder}, rating DESC`;
          break;
        case 'relevance':
        default:
          orderClause += `ts_rank(search_vector, plainto_tsquery('english', $1)) DESC, rating DESC`;
          break;
      }

      // Execute search query
      const searchQuery = `
        SELECT 
          id, name, description, price, original_price, discount_percentage,
          images, category, stock, featured, rating, review_count, sales_count,
          ts_rank(search_vector, plainto_tsquery('english', $1)) as relevance_score,
          created_at, updated_at
        FROM products 
        WHERE ${whereClause}
          AND search_vector @@ plainto_tsquery('english', $1)
        ${orderClause}
        LIMIT $${++paramIndex} OFFSET $${++paramIndex}
      `;

      params.push(limit, offset);

      const results = await sql.unsafe(searchQuery, params);

      // Get total count for pagination
      const countQuery = `
        SELECT COUNT(*) as total
        FROM products 
        WHERE ${whereClause}
          AND search_vector @@ plainto_tsquery('english', $1)
      `;

      const countResult = await sql.unsafe(countQuery, params.slice(0, -2));
      const total = parseInt(countResult[0].total);

      // Track search analytics
      await this.trackSearch(query, userId, results.length);

      // Update popular searches
      this.updatePopularSearches(query);

      return {
        results,
        total,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(total / limit),
        hasMore: offset + limit < total
      };
    } catch (error) {
      logger.error('Search failed:', error);
      throw error;
    }
  }

  // Search suggestions/autocomplete
  async getSearchSuggestions(query, limit = 10) {
    try {
      const cacheKey = `suggestions:${query}:${limit}`;
      const cached = await cacheManager.get(cacheKey);
      
      if (cached) {
        return cached;
      }

      // Get product name suggestions
      const productSuggestions = await sql`
        SELECT DISTINCT name, category
        FROM products 
        WHERE name ILIKE ${`%${query}%`}
          AND status = 'active'
        ORDER BY sales_count DESC, rating DESC
        LIMIT ${Math.ceil(limit / 2)}
      `;

      // Get category suggestions
      const categorySuggestions = await sql`
        SELECT DISTINCT category as name, 'category' as type
        FROM products 
        WHERE category ILIKE ${`%${query}%`}
          AND status = 'active'
        LIMIT ${Math.floor(limit / 2)}
      `;

      const suggestions = [
        ...productSuggestions.map(p => ({ 
          text: p.name, 
          type: 'product',
          category: p.category 
        })),
        ...categorySuggestions.map(c => ({ 
          text: c.name, 
          type: 'category' 
        }))
      ].slice(0, limit);

      // Cache suggestions for 1 hour
      await cacheManager.set(cacheKey, suggestions, 3600);

      return suggestions;
    } catch (error) {
      logger.error('Failed to get search suggestions:', error);
      return [];
    }
  }

  // Search categories
  async searchCategories(query, limit = 10) {
    try {
      const results = await sql`
        SELECT DISTINCT category, COUNT(*) as product_count
        FROM products 
        WHERE category ILIKE ${`%${query}%`}
          AND status = 'active'
        GROUP BY category
        ORDER BY product_count DESC
        LIMIT ${limit}
      `;

      return results;
    } catch (error) {
      logger.error('Category search failed:', error);
      return [];
    }
  }

  // Advanced search with multiple criteria
  async advancedSearch(criteria) {
    try {
      const {
        query,
        categories = [],
        priceRange = {},
        ratings = [],
        features = [],
        brands = [],
        sortBy = 'relevance',
        limit = 20,
        offset = 0
      } = criteria;

      let whereConditions = [`status = 'active'`];
      let params = [];
      let paramIndex = 0;

      // Text search
      if (query) {
        whereConditions.push(`search_vector @@ plainto_tsquery('english', $${++paramIndex})`);
        params.push(query);
      }

      // Categories filter
      if (categories.length > 0) {
        whereConditions.push(`category = ANY($${++paramIndex})`);
        params.push(categories);
      }

      // Price range filter
      if (priceRange.min !== undefined) {
        whereConditions.push(`price >= $${++paramIndex}`);
        params.push(priceRange.min);
      }
      if (priceRange.max !== undefined) {
        whereConditions.push(`price <= $${++paramIndex}`);
        params.push(priceRange.max);
      }

      // Rating filter
      if (ratings.length > 0) {
        const ratingConditions = ratings.map(rating => {
          whereConditions.push(`rating >= $${++paramIndex}`);
          params.push(rating);
          return `rating >= $${paramIndex}`;
        });
      }

      // Features filter (using JSONB)
      if (features.length > 0) {
        features.forEach(feature => {
          whereConditions.push(`specifications @> $${++paramIndex}`);
          params.push(JSON.stringify({ [feature.key]: feature.value }));
        });
      }

      // Brands filter
      if (brands.length > 0) {
        whereConditions.push(`specifications->>'brand' = ANY($${++paramIndex})`);
        params.push(brands);
      }

      const whereClause = whereConditions.join(' AND ');

      // Build sort clause
      let orderClause = 'ORDER BY ';
      switch (sortBy) {
        case 'price_asc':
          orderClause += 'price ASC';
          break;
        case 'price_desc':
          orderClause += 'price DESC';
          break;
        case 'rating':
          orderClause += 'rating DESC, review_count DESC';
          break;
        case 'newest':
          orderClause += 'created_at DESC';
          break;
        case 'popular':
          orderClause += 'sales_count DESC, rating DESC';
          break;
        case 'relevance':
        default:
          if (query) {
            orderClause += `ts_rank(search_vector, plainto_tsquery('english', $1)) DESC, rating DESC`;
          } else {
            orderClause += 'rating DESC, sales_count DESC';
          }
          break;
      }

      // Execute search
      const searchQuery = `
        SELECT 
          id, name, description, price, original_price, discount_percentage,
          images, category, stock, featured, rating, review_count, sales_count,
          specifications, tags,
          ${query ? `ts_rank(search_vector, plainto_tsquery('english', $1)) as relevance_score,` : ''}
          created_at, updated_at
        FROM products 
        WHERE ${whereClause}
        ${orderClause}
        LIMIT $${++paramIndex} OFFSET $${++paramIndex}
      `;

      params.push(limit, offset);

      const results = await sql.unsafe(searchQuery, params);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM products 
        WHERE ${whereClause}
      `;

      const countResult = await sql.unsafe(countQuery, params.slice(0, -2));
      const total = parseInt(countResult[0].total);

      return {
        results,
        total,
        page: Math.floor(offset / limit) + 1,
        totalPages: Math.ceil(total / limit),
        hasMore: offset + limit < total
      };
    } catch (error) {
      logger.error('Advanced search failed:', error);
      throw error;
    }
  }

  // Track search analytics
  async trackSearch(query, userId = null, resultCount = 0) {
    try {
      // Store search in database for analytics
      await sql`
        INSERT INTO search_analytics (
          query, user_id, result_count, searched_at
        ) VALUES (
          ${query}, ${userId}, ${resultCount}, CURRENT_TIMESTAMP
        )
      `;

      // Update in-memory analytics
      const today = new Date().toISOString().split('T')[0];
      const key = `${today}:${query}`;
      
      if (this.searchAnalytics.has(key)) {
        this.searchAnalytics.set(key, this.searchAnalytics.get(key) + 1);
      } else {
        this.searchAnalytics.set(key, 1);
      }
    } catch (error) {
      logger.error('Failed to track search:', error);
    }
  }

  // Update popular searches
  updatePopularSearches(query) {
    const normalizedQuery = query.toLowerCase().trim();
    
    if (this.popularSearches.has(normalizedQuery)) {
      this.popularSearches.set(normalizedQuery, this.popularSearches.get(normalizedQuery) + 1);
    } else {
      this.popularSearches.set(normalizedQuery, 1);
    }

    // Keep only top 100 popular searches
    if (this.popularSearches.size > 100) {
      const sorted = Array.from(this.popularSearches.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 100);
      
      this.popularSearches.clear();
      sorted.forEach(([query, count]) => {
        this.popularSearches.set(query, count);
      });
    }
  }

  // Get popular searches
  getPopularSearches(limit = 10) {
    return Array.from(this.popularSearches.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([query, count]) => ({ query, count }));
  }

  // Get search analytics
  async getSearchAnalytics(startDate, endDate, limit = 50) {
    try {
      const results = await sql`
        SELECT 
          query,
          COUNT(*) as search_count,
          AVG(result_count) as avg_results,
          COUNT(DISTINCT user_id) as unique_users
        FROM search_analytics 
        WHERE searched_at >= ${startDate} 
          AND searched_at <= ${endDate}
        GROUP BY query
        ORDER BY search_count DESC
        LIMIT ${limit}
      `;

      return results;
    } catch (error) {
      logger.error('Failed to get search analytics:', error);
      return [];
    }
  }

  // Clear search cache
  clearSearchCache() {
    this.searchCache.clear();
    logger.info('Search cache cleared');
  }
}

export default new SearchService();
