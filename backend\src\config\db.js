import {neon} from "@neondatabase/serverless";
import "dotenv/config";

// create a connection using out database url
export const sql = neon(process.env.DATABASE_URL);

export async function initDB() {
    try {
        await sql`CREATE TABLE IF NOT EXISTS transactions(
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            title VARCHAR(255) NOT NULL,
            amount DECIMAL NOT NULL,
            category VARCHAR(255) NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;

        await sql`CREATE TABLE IF NOT EXISTS categories(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`;

        await sql`CREATE TABLE IF NOT EXISTS subcategories(
            id SERIAL PRIMARY KEY,
            name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
            category_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
        )`;

        await sql`CREATE TABLE IF NOT EXISTS products(
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT NOT NULL,
            price DECIMAL NOT NULL,
            original_price DECIMAL NOT NULL,
            discount_percentage DECIMAL NOT NULL,
            images JSONB NOT NULL,
            category VARCHAR(255) NOT NULL,
            stock INTEGER NOT NULL,
            featured BOOLEAN NOT NULL,
            status VARCHAR(255) NOT NULL,
            seller_id VARCHAR(255) NOT NULL,
            specifications JSONB NOT NULL,
            tags JSONB NOT NULL,
            dynamic_pricing JSONB NOT NULL,
            created_at DATE NOT NULL DEFAULT CURRENT_DATE,
            updated_at DATE NOT NULL DEFAULT CURRENT_DATE
        )`;

        console.log('Database initialized successfully');
    } catch (error) {
        console.log('Error initializing DB', error);
        process.exit(1);//status code 1 means falure 0 means success 
    }
}

