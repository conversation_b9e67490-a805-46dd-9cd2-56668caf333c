import { useUser } from '@clerk/clerk-expo';
import { ScrollView, View, Alert, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { useState, useEffect } from 'react';
import { useCart } from '../../contexts/CartContext';
import { Box } from "@/components/ui/box";
import { Heading } from "@/components/ui/heading";
import { Text } from "@/components/ui/text";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Image } from "@/components/ui/image";
import { Button, ButtonText } from "@/components/ui/button";
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/colors';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Safe function to format price
const formatPrice = (price) => {
  try {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return isNaN(numPrice) ? '0.00' : numPrice.toFixed(2);
  } catch (error) {
    console.warn('Error formatting price:', error);
    return '0.00';
  }
};

// Safe function to get product image
const getProductImage = (product) => {
  try {
    if (!product.images) {
      return 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
    }
    
    if (typeof product.images === 'string') {
      try {
        const parsedImages = JSON.parse(product.images);
        return Array.isArray(parsedImages) && parsedImages.length > 0 
          ? parsedImages[0] 
          : 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
      } catch (parseError) {
        return product.images.startsWith('http') 
          ? product.images 
          : 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
      }
    }
    
    if (Array.isArray(product.images) && product.images.length > 0) {
      return product.images[0];
    }
    
    return 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
  } catch (error) {
    console.warn('Error getting product image:', error);
    return 'https://via.placeholder.com/100x100/f0f0f0/999999?text=No+Image';
  }
};

const WishlistItem = ({ item, onRemove, onAddToCart, onPress }) => {
  const discountPercentage = item.discount_percentage || 
    (item.original_price > item.price
      ? Math.round(((item.original_price - item.price) / item.original_price) * 100)
      : 0);

  return (
    <Box className="bg-white p-4 rounded-lg mb-3 shadow-sm border border-gray-200">
      <HStack className="space-x-4">
        {/* Product Image */}
        <TouchableOpacity onPress={() => onPress(item)}>
          <Image
            source={{ uri: getProductImage(item) }}
            alt={item.name}
            style={styles.wishlistItemImage}
          />
        </TouchableOpacity>

        {/* Product Info */}
        <Box className="flex-1">
          <TouchableOpacity onPress={() => onPress(item)}>
            <Text className="text-lg font-semibold text-gray-900 mb-1">
              {item.name}
            </Text>
          </TouchableOpacity>
          
          <Text className="text-sm text-gray-500 mb-2">
            {item.category}
          </Text>
          
          <Text className="text-sm text-gray-600 mb-3" numberOfLines={2}>
            {item.description || 'Ürün açıklaması bulunmamaktadır.'}
          </Text>

          {/* Price Section */}
          <HStack className="items-center justify-between mb-3">
            <VStack>
              <Text className="text-xl font-bold text-green-600">
                ₺{formatPrice(item.price)}
              </Text>
              {item.original_price > item.price && (
                <HStack className="items-center space-x-2">
                  <Text className="text-sm text-gray-400 line-through">
                    ₺{formatPrice(item.original_price)}
                  </Text>
                  {discountPercentage > 0 && (
                    <Text className="text-xs text-red-500 font-bold">
                      %{discountPercentage} İndirim
                    </Text>
                  )}
                </HStack>
              )}
            </VStack>
            
            {item.stock > 0 ? (
              <Text className="text-sm text-green-600 font-semibold">
                Stokta {item.stock}
              </Text>
            ) : (
              <Text className="text-sm text-red-500 font-semibold">
                Stokta Yok
              </Text>
            )}
          </HStack>

          {/* Action Buttons */}
          <HStack className="space-x-2">
            <Button
              size="sm"
              className="flex-1 bg-blue-600"
              onPress={() => onAddToCart(item)}
              disabled={item.stock <= 0}
            >
              <ButtonText size="sm">Sepete Ekle</ButtonText>
            </Button>
            
            <TouchableOpacity 
              style={styles.removeButton}
              onPress={() => onRemove(item.id)}
            >
              <Ionicons name="heart" size={20} color={COLORS.error || '#FF4444'} />
            </TouchableOpacity>
          </HStack>
        </Box>
      </HStack>
    </Box>
  );
};

export default function WishlistPage() {
  const { user } = useUser();
  const { addToCart } = useCart();
  const router = useRouter();
  const [wishlistItems, setWishlistItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWishlist();
  }, []);

  const loadWishlist = async () => {
    try {
      const savedWishlist = await AsyncStorage.getItem('wishlist');
      if (savedWishlist) {
        setWishlistItems(JSON.parse(savedWishlist));
      }
    } catch (error) {
      console.error('Error loading wishlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveWishlist = async (items) => {
    try {
      await AsyncStorage.setItem('wishlist', JSON.stringify(items));
    } catch (error) {
      console.error('Error saving wishlist:', error);
    }
  };

  const removeFromWishlist = (productId) => {
    Alert.alert(
      'Favorilerden Kaldır',
      'Bu ürünü favorilerden kaldırmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Kaldır', 
          style: 'destructive', 
          onPress: () => {
            const updatedItems = wishlistItems.filter(item => item.id !== productId);
            setWishlistItems(updatedItems);
            saveWishlist(updatedItems);
            Alert.alert('Başarılı', 'Ürün favorilerden kaldırıldı');
          }
        }
      ]
    );
  };

  const handleAddToCart = (product) => {
    addToCart(product, 1);
    Alert.alert('Sepete Eklendi', `${product.name} sepete eklendi`);
  };

  const handleProductPress = (product) => {
    router.push(`/product/${product.id}`);
  };

  const clearWishlist = () => {
    Alert.alert(
      'Favorileri Temizle',
      'Tüm favori ürünleri kaldırmak istediğinizden emin misiniz?',
      [
        { text: 'İptal', style: 'cancel' },
        { 
          text: 'Temizle', 
          style: 'destructive', 
          onPress: () => {
            setWishlistItems([]);
            saveWishlist([]);
            Alert.alert('Başarılı', 'Tüm favoriler temizlendi');
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center">
          <Text className="text-gray-500">Favoriler yükleniyor...</Text>
        </Box>
      </View>
    );
  }

  if (wishlistItems.length === 0) {
    return (
      <View style={styles.container}>
        <Box className="flex-1 justify-center items-center px-6">
          <Ionicons name="heart-outline" size={80} color={COLORS.textLight} />
          <Heading size="xl" className="text-gray-500 mt-4 mb-2 text-center">
            Favori Listeniz Boş
          </Heading>
          <Text className="text-gray-400 text-center mb-6">
            Beğendiğiniz ürünleri favorilere ekleyerek daha sonra kolayca bulabilirsiniz
          </Text>
          <Button onPress={() => router.push('/')}>
            <ButtonText>Alışverişe Başla</ButtonText>
          </Button>
        </Box>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <Box className="bg-white p-4 border-b border-gray-200">
        <HStack className="items-center justify-between">
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <Heading size="lg" className="text-gray-900">
            Favorilerim ({wishlistItems.length})
          </Heading>
          
          <TouchableOpacity onPress={clearWishlist}>
            <Text className="text-red-500 font-semibold">Temizle</Text>
          </TouchableOpacity>
        </HStack>
      </Box>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Box className="p-4">
          {wishlistItems.map((item) => (
            <WishlistItem
              key={item.id}
              item={item}
              onRemove={removeFromWishlist}
              onAddToCart={handleAddToCart}
              onPress={handleProductPress}
            />
          ))}
        </Box>
      </ScrollView>
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  wishlistItemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  removeButton: {
    padding: 8,
    backgroundColor: 'rgba(255, 68, 68, 0.1)',
    borderRadius: 8,
  },
};
