{"dashboard": {"id": null, "title": "Wallet Application Dashboard", "tags": ["wallet", "nodejs", "api"], "timezone": "browser", "panels": [{"id": 1, "title": "API Response Time", "type": "graph", "targets": [{"expr": "rate(http_request_duration_ms_sum[5m]) / rate(http_request_duration_ms_count[5m])", "legendFormat": "{{endpoint}}"}], "yAxes": [{"label": "Response Time (ms)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "Request Rate", "type": "graph", "targets": [{"expr": "rate(http_requests_total[5m])", "legendFormat": "{{endpoint}}"}], "yAxes": [{"label": "Requests/sec", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Memory Usage", "type": "graph", "targets": [{"expr": "nodejs_heap_used_bytes / 1024 / 1024", "legendFormat": "Heap Used (MB)"}, {"expr": "nodejs_heap_total_bytes / 1024 / 1024", "legendFormat": "Heap Total (MB)"}], "yAxes": [{"label": "Memory (MB)", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "CPU Usage", "type": "graph", "targets": [{"expr": "rate(nodejs_cpu_user_seconds_total[5m]) * 100", "legendFormat": "User CPU %"}, {"expr": "rate(nodejs_cpu_system_seconds_total[5m]) * 100", "legendFormat": "System CPU %"}], "yAxes": [{"label": "CPU %", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "Error Rate", "type": "singlestat", "targets": [{"expr": "rate(http_requests_total{code=~\"4..|5..\"}[5m]) / rate(http_requests_total[5m]) * 100", "legendFormat": "Error Rate %"}], "valueName": "current", "format": "percent", "thresholds": "5,10", "colorBackground": true, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 16}}, {"id": 6, "title": "Active Users", "type": "singlestat", "targets": [{"expr": "count(increase(http_requests_total[1h]))", "legendFormat": "Active Users"}], "valueName": "current", "format": "short", "gridPos": {"h": 4, "w": 6, "x": 6, "y": 16}}, {"id": 7, "title": "Database Connections", "type": "singlestat", "targets": [{"expr": "pg_stat_database_numbackends", "legendFormat": "DB Connections"}], "valueName": "current", "format": "short", "gridPos": {"h": 4, "w": 6, "x": 12, "y": 16}}, {"id": 8, "title": "<PERSON><PERSON> Hit Rate", "type": "singlestat", "targets": [{"expr": "redis_keyspace_hits_total / (redis_keyspace_hits_total + redis_keyspace_misses_total) * 100", "legendFormat": "Cache Hit Rate %"}], "valueName": "current", "format": "percent", "thresholds": "80,90", "colorBackground": true, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s"}}