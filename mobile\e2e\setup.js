const { device, expect, element, by, waitFor } = require('detox');

beforeAll(async () => {
  await device.launchApp();
});

beforeEach(async () => {
  await device.reloadReactNative();
});

// Custom matchers and utilities
global.waitForElementToBeVisible = async (elementMatcher, timeout = 10000) => {
  await waitFor(element(elementMatcher))
    .toBeVisible()
    .withTimeout(timeout);
};

global.waitForElementToExist = async (elementMatcher, timeout = 10000) => {
  await waitFor(element(elementMatcher))
    .toExist()
    .withTimeout(timeout);
};

global.scrollToElement = async (scrollViewMatcher, elementMatcher) => {
  await waitFor(element(elementMatcher))
    .toBeVisible()
    .whileElement(scrollViewMatcher)
    .scroll(200, 'down');
};

// Test data
global.testData = {
  validUser: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User'
  },
  invalidUser: {
    email: '<EMAIL>',
    password: 'wrongpassword'
  },
  testProduct: {
    name: 'Test Product',
    description: 'This is a test product',
    price: '99.99'
  }
};
