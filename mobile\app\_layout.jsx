import { Slot } from "expo-router";
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import SafeScreen from "../components/SafeScreen";
import { ClerkProvider } from '@clerk/clerk-expo';
import { tokenCache } from '@clerk/clerk-expo/token-cache';
import { CartProvider } from '../contexts/CartContext';


const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY;


export default function Rootlayout() {
  return (
    <GluestackUIProvider mode="light">
      <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
        <CartProvider>
          <SafeScreen>
            <Slot />
          </SafeScreen>
        </CartProvider>
      </ClerkProvider>
    </GluestackUIProvider>
  );
}
