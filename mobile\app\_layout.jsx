import { Slot } from "expo-router";
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
import SafeScreen from "../components/SafeScreen";
import { ClerkProvider } from '@clerk/clerk-expo';
import { tokenCache } from '@clerk/clerk-expo/token-cache';
import { CartProvider } from '../contexts/CartContext';


const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY;

console.log('Clerk publishable key loaded:', publishableKey ? 'Yes' : 'No');
console.log('API URL loaded:', process.env.EXPO_PUBLIC_API_URL || 'Not found');

export default function Rootlayout() {
  return (
    <GluestackUIProvider mode="light">
      <ClerkProvider tokenCache={tokenCache} publishableKey={publishableKey}>
        <CartProvider>
          <SafeScreen>
            <Slot />
          </SafeScreen>
        </CartProvider>
      </ClerkProvider>
    </GluestackUIProvider>
  );
}
