import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, TouchableOpacity, Alert, RefreshControl, Image, Pressable } from 'react-native';
import { useUser, useAuth } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { Navbar } from '../../../../components/Navbar';
import { ConfirmationModal } from '../../../../components/ConfirmationModal';
import { Toast } from '../../../../components/Toast';
import { useToast } from '../../../../hooks/useToast';

export default function UsersManagement() {
  const { user } = useUser();
  const { getToken } = useAuth();
  const router = useRouter();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const { toast, showSuccess, showError, hideToast } = useToast();
  const [deleteModal, setDeleteModal] = useState({ visible: false, user: null });

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_URL}/users`);
      
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        Alert.alert('Hata', 'Kullanıcılar yüklenemedi');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      Alert.alert('Hata', 'Kullanıcılar yüklenirken bir hata oluştu');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchUsers();
    setRefreshing(false);
  };

  const handleAddUser = () => {
    router.push('/dashboard/users/add');
  };

  const handleEditUser = (userData) => {
    router.push(`/dashboard/users/edit/${userData.id}`);
  };

  const handleDeleteUser = async (userData) => {
    console.log('Delete user clicked:', userData);
    setDeleteModal({ visible: true, user: userData });
  };

  const confirmDeleteUser = async () => {
    if (!deleteModal.user) return;

    try {
      const token = await getToken();
      const response = await fetch(`${API_URL}/users/${deleteModal.user.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        showSuccess('Kullanıcı başarıyla silindi');
        fetchUsers();
      } else {
        const errorData = await response.json();
        showError(errorData.message || 'Kullanıcı silinirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      showError('Kullanıcı silinirken bir hata oluştu');
    } finally {
      setDeleteModal({ visible: false, user: null });
    }
  };

  const getUserAvatar = (userData) => {
    if (userData.avatar) {
      return userData.avatar;
    }
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.email)}&background=random`;
  };

  const getUserRole = (role) => {
    const roles = {
      admin: 'Yönetici',
      user: 'Kullanıcı',
      moderator: 'Moderatör'
    };
    return roles[role] || 'Kullanıcı';
  };

  const getRoleColor = (role) => {
    const colors = {
      admin: COLORS.error,
      moderator: COLORS.warning,
      user: COLORS.primary
    };
    return colors[role] || COLORS.primary;
  };

  const renderUserItem = ({ item }) => (
    <View style={styles.userItem}>
      <Image source={{ uri: getUserAvatar(item) }} style={styles.userAvatar} />
      
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{item.first_name} {item.last_name}</Text>
        <Text style={styles.userEmail}>{item.email}</Text>
        <View style={styles.userMeta}>
          <View style={[styles.roleTag, { backgroundColor: getRoleColor(item.role) + '20' }]}>
            <Text style={[styles.roleText, { color: getRoleColor(item.role) }]}>
              {getUserRole(item.role)}
            </Text>
          </View>
          <Text style={styles.userDate}>
            {new Date(item.created_at).toLocaleDateString('tr-TR')}
          </Text>
        </View>
      </View>
      
      <View style={styles.userActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.editButton]}
          onPress={() => {
            console.log('Edit button clicked for user:', item.id);
            handleEditUser(item);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="pencil" size={16} color={COLORS.white} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => {
            console.log('Delete button clicked for user:', item.id);
            handleDeleteUser(item);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="trash" size={16} color={COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      <Navbar user={user} router={router} />
      
      <View style={styles.header}>
        <Text style={styles.title}>Kullanıcı Yönetimi</Text>
        <TouchableOpacity style={styles.addButton} onPress={handleAddUser}>
          <Ionicons name="add" size={24} color={COLORS.white} />
          <Text style={styles.addButtonText}>Yeni Kullanıcı</Text>
        </TouchableOpacity>
      </View>

      <FlatList
        data={users}
        renderItem={renderUserItem}
        keyExtractor={(item) => item.id.toString()}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color={COLORS.textLight} />
            <Text style={styles.emptyText}>Henüz kullanıcı bulunmuyor</Text>
            <TouchableOpacity style={styles.emptyButton} onPress={handleAddUser}>
              <Text style={styles.emptyButtonText}>İlk Kullanıcıyı Ekle</Text>
            </TouchableOpacity>
          </View>
        }
      />

      <Toast
        visible={toast.visible}
        message={toast.message}
        type={toast.type}
        onHide={hideToast}
      />

      <ConfirmationModal
        visible={deleteModal.visible}
        title="Kullanıcıyı Sil"
        message={`"${deleteModal.user?.email}" kullanıcısını silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
        confirmText="Sil"
        cancelText="İptal"
        onConfirm={confirmDeleteUser}
        onCancel={() => setDeleteModal({ visible: false, user: null })}
        type="danger"
      />
    </View>
  );
}

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: COLORS.card,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    cursor: 'pointer',
  },
  addButtonText: {
    color: COLORS.white,
    fontWeight: '600',
    marginLeft: 8,
  },
  list: {
    flex: 1,
  },
  listContent: {
    padding: 20,
  },
  userItem: {
    flexDirection: 'row',
    backgroundColor: COLORS.card,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: COLORS.background,
  },
  userInfo: {
    flex: 1,
    marginLeft: 12,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: COLORS.textLight,
    marginBottom: 8,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  roleTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '600',
  },
  userDate: {
    fontSize: 11,
    color: COLORS.textLight,
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    backgroundColor: COLORS.warning,
  },
  deleteButton: {
    backgroundColor: COLORS.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.textLight,
    marginTop: 16,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    cursor: 'pointer',
  },
  emptyButtonText: {
    color: COLORS.white,
    fontWeight: '600',
  },
};
