{"name": "wallet-app", "version": "1.0.0", "description": "React Native Wallet Application", "main": "index.js", "scripts": {"prepare": "husky install", "lint": "npm run lint:backend && npm run lint:mobile", "lint:backend": "cd backend && npm run lint", "lint:mobile": "cd mobile && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:mobile", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:mobile": "cd mobile && npm run lint:fix", "format": "npm run format:backend && npm run format:mobile", "format:backend": "cd backend && npm run format", "format:mobile": "cd mobile && npm run format", "test": "npm run test:backend && npm run test:mobile", "test:backend": "cd backend && npm test", "test:mobile": "cd mobile && npm test -- --watchAll=false", "test:coverage": "npm run test:coverage:backend && npm run test:coverage:mobile", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:mobile": "cd mobile && npm run test:coverage", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:mobile\"", "dev:backend": "cd backend && npm run dev", "dev:mobile": "cd mobile && npm start", "build": "npm run build:backend", "build:backend": "cd backend && npm run build", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:prod": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "./scripts/setup.sh", "deploy:staging": "./scripts/deploy.sh staging", "deploy:production": "./scripts/deploy.sh production"}, "lint-staged": {"backend/**/*.{js,jsx,ts,tsx}": ["cd backend && npm run lint:fix", "cd backend && npm run format", "git add"], "mobile/**/*.{js,jsx,ts,tsx}": ["cd mobile && npm run lint:fix", "cd mobile && npm run format", "git add"], "**/*.{json,md,yml,yaml}": ["prettier --write", "git add"]}, "devDependencies": {"concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "typescript": "~5.8.3", "@types/react": "~19.0.10"}, "keywords": ["react-native", "wallet", "mobile-app", "expo", "nodejs", "postgresql"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/wallet-app.git"}, "dependencies": {"expo": "^53.0.9"}}