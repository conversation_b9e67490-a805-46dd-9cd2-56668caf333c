import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TextInput, TouchableOpacity, ScrollView, Alert, ActivityIndicator, KeyboardAvoidingView, Platform } from 'react-native';
import { useUser } from '@clerk/clerk-expo';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/colors';
import { API_URL } from '@/constants/api';
import { useCategories } from '../../../../../hooks/useCategories';
import { Navbar } from '../../../../../components/Navbar';
import { styles } from '../../../../../assets/styles/form.styles';

export default function EditProduct() {
  const { user } = useUser();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const { categories } = useCategories();
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [productLoading, setProductLoading] = useState(true);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    original_price: '',
    category: '',
    stock: '',
    featured: false,
    specifications: '',
    tags: '',
  });

  const fetchProduct = useCallback(async () => {
    try {
      setProductLoading(true);
      const response = await fetch(`${API_URL}/products/${id}`);
      if (response.ok) {
        const product = await response.json();
        setFormData({
          name: product.name || '',
          description: product.description || '',
          price: product.price?.toString() || '',
          original_price: product.original_price?.toString() || '',
          category: product.category || '',
          stock: product.stock?.toString() || '',
          featured: product.featured || false,
          specifications: typeof product.specifications === 'object'
            ? product.specifications?.details || ''
            : product.specifications || '',
          tags: Array.isArray(product.tags)
            ? product.tags.join(', ')
            : product.tags || '',
        });
      } else {
        Alert.alert('Hata', 'Ürün bilgileri yüklenemedi');
        router.back();
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      Alert.alert('Hata', 'Ürün bilgileri yüklenirken bir hata oluştu');
      router.back();
    } finally {
      setProductLoading(false);
    }
  }, [id, router]);

  useEffect(() => {
    if (id) {
      fetchProduct();
    }
  }, [id, fetchProduct]);

  const handleInputChange = useCallback((field, value) => {
    console.log(`Field: ${field}, Value: ${value}`);
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  const toggleCategoryDropdown = useCallback(() => {
    setShowCategoryDropdown(prev => !prev);
  }, []);

  const handleCategorySelect = useCallback((categoryName) => {
    handleInputChange('category', categoryName);
    setShowCategoryDropdown(false);
  }, [handleInputChange]);

  const handleSubmit = useCallback(async () => {
    try {
      if (!formData.name || !formData.description || !formData.price || !formData.category) {
        Alert.alert('Hata', 'Lütfen tüm zorunlu alanları doldurun');
        return;
      }
      const price = parseFloat(formData.price);
      const stock = parseInt(formData.stock) || 0;
      if (isNaN(price) || price < 0) {
        Alert.alert('Hata', 'Geçerli bir fiyat girin');
        return;
      }
      if (stock < 0) {
        Alert.alert('Hata', 'Stok miktarı negatif olamaz');
        return;
      }

      setLoading(true);
      const productData = {
        name: formData.name,
        description: formData.description,
        price,
        original_price: formData.original_price ? parseFloat(formData.original_price) : price,
        category: formData.category,
        stock,
        featured: formData.featured,
        specifications: formData.specifications || '',
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
      };
      console.log('Sending productData:', productData);
      const response = await fetch(`${API_URL}/products/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(productData),
      });
      if (response.ok) {
        Alert.alert('Başarılı', 'Ürün başarıyla güncellendi', [
          {
            text: 'Tamam',
            onPress: () => {
              router.setParams({ refresh: true });
              router.back();
            },
          },
        ]);
      } else {
        const errorData = await response.json();
        Alert.alert('Hata', errorData.message || 'Ürün güncellenirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      Alert.alert('Hata', 'Ürün güncellenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [formData, id, router]);

  if (productLoading) {
    return (
      <View style={styles.container}>
        <Navbar
          user={user}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          router={router}
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Ürün bilgileri yükleniyor...</Text>
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
      keyboardVerticalOffset={100}
    >
      <Navbar
        user={user}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        router={router}
      />
      <ScrollView style={styles.formContainer} keyboardShouldPersistTaps="handled">
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Ürün Düzenle</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Ürün Adı *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Ürün adını girin"
              placeholderTextColor={COLORS.textLight}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Açıklama *</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Ürün açıklamasını girin"
              placeholderTextColor={COLORS.textLight}
              multiline
              numberOfLines={4}
              blurOnSubmit={true}
              returnKeyType="done"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Fiyat *</Text>
              <TextInput
                style={styles.input}
                value={formData.price}
                onChangeText={(value) => handleInputChange('price', value)}
                placeholder="0.00"
                placeholderTextColor={COLORS.textLight}
                keyboardType="numeric"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            <View style={[styles.inputGroup, styles.halfWidth]}>
              <Text style={styles.label}>Orijinal Fiyat</Text>
              <TextInput
                style={styles.input}
                value={formData.original_price}
                onChangeText={(value) => handleInputChange('original_price', value)}
                placeholder="0.00"
                placeholderTextColor={COLORS.textLight}
                keyboardType="numeric"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kategori *</Text>
            <View style={styles.dropdownContainer}>
              <TouchableOpacity style={styles.dropdown} onPress={toggleCategoryDropdown}>
                <Text style={[styles.dropdownText, !formData.category && styles.dropdownPlaceholder]}>
                  {formData.category || 'Kategori seçin'}
                </Text>
                <Ionicons
                  name={showCategoryDropdown ? 'chevron-up' : 'chevron-down'}
                  size={20}
                  color={COLORS.textLight}
                />
              </TouchableOpacity>
              {showCategoryDropdown && (
                <View style={styles.dropdownList}>
                  {categories.map((category) => (
                    <TouchableOpacity
                      key={category.id}
                      style={styles.dropdownItem}
                      onPress={() => handleCategorySelect(category.name)}
                    >
                      <Text style={styles.dropdownItemText}>{category.name}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              )}
            </View>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Stok Miktarı</Text>
            <TextInput
              style={styles.input}
              value={formData.stock}
              onChangeText={(value) => handleInputChange('stock', value)}
              placeholder="0"
              placeholderTextColor={COLORS.textLight}
              keyboardType="numeric"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Özellikler</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.specifications}
              onChangeText={(value) => handleInputChange('specifications', value)}
              placeholder="Ürün özelliklerini girin"
              placeholderTextColor={COLORS.textLight}
              multiline
              numberOfLines={3}
              blurOnSubmit={true}
              returnKeyType="done"
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Etiketler (virgülle ayırın)</Text>
            <TextInput
              style={styles.input}
              value={formData.tags}
              onChangeText={(value) => handleInputChange('tags', value)}
              placeholder="etiket1, etiket2, etiket3"
              placeholderTextColor={COLORS.textLight}
              autoCapitalize="none"
              autoCorrect={false}
            />
          </View>

          <View style={styles.checkboxGroup}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => handleInputChange('featured', !formData.featured)}
            >
              <Ionicons
                name={formData.featured ? 'checkbox' : 'square-outline'}
                size={24}
                color={COLORS.primary}
              />
              <Text style={styles.checkboxLabel}>Öne çıkan ürün</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Güncelleniyor...' : 'Ürünü Güncelle'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}
    </KeyboardAvoidingView>
  );
}


  