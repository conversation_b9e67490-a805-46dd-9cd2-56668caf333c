import {
  validateUserRegistration,
  validateUserLogin,
  validateProduct,
  validateCategory,
  validateTransaction
} from '../../../src/middleware/validation.js';
import { AppError } from '../../../src/middleware/errorHandler.js';

describe('Validation Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = { body: {} };
    res = {};
    next = jest.fn();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('validateUserRegistration', () => {
    it('should pass validation with valid user data', () => {
      req.body = {
        name: '<PERSON>',
        email: '<EMAIL>',
        username: 'johndo<PERSON>',
        password: 'password123'
      };

      validateUserRegistration(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(req.body.role).toBe('user'); // default value
    });

    it('should fail validation with missing required fields', () => {
      req.body = {
        name: '<PERSON>'
        // missing email, username, password
      };

      validateUserRegistration(req, res, next);

      expect(next).toHave<PERSON>eenCalledWith(expect.any(AppError));
      expect(next.mock.calls[0][0].statusCode).toBe(400);
    });

    it('should fail validation with invalid email', () => {
      req.body = {
        name: 'John Doe',
        email: 'invalid-email',
        username: 'johndoe',
        password: 'password123'
      };

      validateUserRegistration(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
      expect(next.mock.calls[0][0].message).toContain('email');
    });

    it('should fail validation with short password', () => {
      req.body = {
        name: 'John Doe',
        email: '<EMAIL>',
        username: 'johndoe',
        password: '123'
      };

      validateUserRegistration(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
      expect(next.mock.calls[0][0].message).toContain('password');
    });
  });

  describe('validateUserLogin', () => {
    it('should pass validation with valid login data', () => {
      req.body = {
        emailOrUsername: '<EMAIL>',
        password: 'password123'
      };

      validateUserLogin(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should fail validation with missing credentials', () => {
      req.body = {
        emailOrUsername: '<EMAIL>'
        // missing password
      };

      validateUserLogin(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
    });
  });

  describe('validateProduct', () => {
    it('should pass validation with valid product data', () => {
      req.body = {
        name: 'Test Product',
        description: 'This is a test product description',
        price: 99.99,
        category: 'electronics',
        seller_id: 'seller123'
      };

      validateProduct(req, res, next);

      expect(next).toHaveBeenCalledWith();
      expect(req.body.stock).toBe(0); // default value
      expect(req.body.featured).toBe(false); // default value
      expect(req.body.status).toBe('active'); // default value
    });

    it('should fail validation with missing required fields', () => {
      req.body = {
        name: 'Test Product'
        // missing description, price, category, seller_id
      };

      validateProduct(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
    });

    it('should fail validation with negative price', () => {
      req.body = {
        name: 'Test Product',
        description: 'This is a test product description',
        price: -10,
        category: 'electronics',
        seller_id: 'seller123'
      };

      validateProduct(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
      expect(next.mock.calls[0][0].message).toContain('positive');
    });

    it('should fail validation with short description', () => {
      req.body = {
        name: 'Test Product',
        description: 'Short',
        price: 99.99,
        category: 'electronics',
        seller_id: 'seller123'
      };

      validateProduct(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
    });
  });

  describe('validateCategory', () => {
    it('should pass validation with valid category data', () => {
      req.body = {
        name: 'Electronics'
      };

      validateCategory(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should fail validation with missing name', () => {
      req.body = {};

      validateCategory(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
    });

    it('should fail validation with short name', () => {
      req.body = {
        name: 'A'
      };

      validateCategory(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
    });
  });

  describe('validateTransaction', () => {
    it('should pass validation with valid transaction data', () => {
      req.body = {
        user_id: 'user123',
        title: 'Test Transaction',
        amount: 100.50,
        category: 'income'
      };

      validateTransaction(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should fail validation with missing required fields', () => {
      req.body = {
        title: 'Test Transaction'
        // missing user_id, amount, category
      };

      validateTransaction(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(AppError));
    });
  });
});
