import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Wallet API',
      version: '1.0.0',
      description: 'A comprehensive wallet and e-commerce API',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
    },
    servers: [
      {
        url: process.env.API_URL || 'http://localhost:5001',
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        User: {
          type: 'object',
          required: ['name', 'email', 'username', 'password'],
          properties: {
            id: {
              type: 'integer',
              description: 'User ID',
            },
            name: {
              type: 'string',
              description: 'User full name',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
            },
            username: {
              type: 'string',
              description: 'Unique username',
            },
            role: {
              type: 'string',
              enum: ['user', 'admin', 'seller'],
              description: 'User role',
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'suspended'],
              description: 'User status',
            },
          },
        },
        Product: {
          type: 'object',
          required: ['name', 'description', 'price', 'category', 'seller_id'],
          properties: {
            id: {
              type: 'integer',
              description: 'Product ID',
            },
            name: {
              type: 'string',
              description: 'Product name',
            },
            description: {
              type: 'string',
              description: 'Product description',
            },
            price: {
              type: 'number',
              format: 'decimal',
              description: 'Product price',
            },
            original_price: {
              type: 'number',
              format: 'decimal',
              description: 'Original price before discount',
            },
            discount_percentage: {
              type: 'number',
              minimum: 0,
              maximum: 100,
              description: 'Discount percentage',
            },
            images: {
              type: 'array',
              items: {
                type: 'string',
                format: 'uri',
              },
              description: 'Product images',
            },
            category: {
              type: 'string',
              description: 'Product category',
            },
            stock: {
              type: 'integer',
              minimum: 0,
              description: 'Available stock',
            },
            featured: {
              type: 'boolean',
              description: 'Is featured product',
            },
            status: {
              type: 'string',
              enum: ['active', 'inactive', 'draft'],
              description: 'Product status',
            },
            seller_id: {
              type: 'string',
              description: 'Seller ID',
            },
          },
        },
        Transaction: {
          type: 'object',
          required: ['user_id', 'title', 'amount', 'category'],
          properties: {
            id: {
              type: 'integer',
              description: 'Transaction ID',
            },
            user_id: {
              type: 'string',
              description: 'User ID',
            },
            title: {
              type: 'string',
              description: 'Transaction title',
            },
            amount: {
              type: 'number',
              format: 'decimal',
              description: 'Transaction amount',
            },
            category: {
              type: 'string',
              description: 'Transaction category',
            },
            created_at: {
              type: 'string',
              format: 'date',
              description: 'Creation date',
            },
          },
        },
        Error: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              description: 'Error status',
            },
            message: {
              type: 'string',
              description: 'Error message',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(options);

export { swaggerUi, specs };
