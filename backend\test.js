console.log('Test file working!');

const express = require('express');
const cors = require('cors');

console.log('Starting server...');

const app = express();
const PORT = 5001;

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
    console.log('Root endpoint called');
    res.json({ message: 'Server working!' });
});

app.get('/api/categories', (req, res) => {
    console.log('Categories endpoint called');
    res.json([
        { id: 1, name: 'Test Category', created_at: new Date() }
    ]);
});

app.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
});
