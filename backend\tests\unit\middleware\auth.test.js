import jwt from 'jsonwebtoken';
import { protect, restrictTo, signToken } from '../../../src/middleware/auth.js';
import { AppError } from '../../../src/middleware/errorHandler.js';

// Mock dependencies
jest.mock('../../../src/config/db.js', () => ({
  sql: jest.fn()
}));

jest.mock('jsonwebtoken');

describe('Auth Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      headers: {},
      cookies: {},
      user: null
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
      cookie: jest.fn()
    };
    next = jest.fn();
    
    // Set up environment variables
    process.env.JWT_SECRET = 'test-secret';
    process.env.JWT_EXPIRES_IN = '24h';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('signToken', () => {
    it('should create a JWT token', () => {
      const userId = 123;
      const mockToken = 'mock-jwt-token';
      
      jwt.sign.mockReturnValue(mockToken);
      
      const token = signToken(userId);
      
      expect(jwt.sign).toHaveBeenCalledWith(
        { id: userId },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN }
      );
      expect(token).toBe(mockToken);
    });
  });

  describe('protect middleware', () => {
    it('should call next with error if no token provided', async () => {
      await protect(req, res, next);
      
      expect(next).toHaveBeenCalledWith(
        expect.any(AppError)
      );
      expect(next.mock.calls[0][0].message).toBe(
        'You are not logged in! Please log in to get access.'
      );
      expect(next.mock.calls[0][0].statusCode).toBe(401);
    });

    it('should extract token from Authorization header', async () => {
      const mockToken = 'valid-token';
      const mockDecoded = { id: 123 };
      const mockUser = { id: 123, name: 'Test User' };

      req.headers.authorization = `Bearer ${mockToken}`;
      jwt.verify.mockReturnValue(mockDecoded);
      
      // Mock database query
      const { sql } = await import('../../../src/config/db.js');
      sql.mockResolvedValue([mockUser]);

      await protect(req, res, next);

      expect(jwt.verify).toHaveBeenCalledWith(mockToken, process.env.JWT_SECRET);
      expect(req.user).toEqual(mockUser);
      expect(next).toHaveBeenCalledWith();
    });

    it('should extract token from cookies', async () => {
      const mockToken = 'valid-token';
      const mockDecoded = { id: 123 };
      const mockUser = { id: 123, name: 'Test User' };

      req.cookies.jwt = mockToken;
      jwt.verify.mockReturnValue(mockDecoded);
      
      const { sql } = await import('../../../src/config/db.js');
      sql.mockResolvedValue([mockUser]);

      await protect(req, res, next);

      expect(jwt.verify).toHaveBeenCalledWith(mockToken, process.env.JWT_SECRET);
      expect(req.user).toEqual(mockUser);
      expect(next).toHaveBeenCalledWith();
    });

    it('should call next with error if user not found', async () => {
      const mockToken = 'valid-token';
      const mockDecoded = { id: 123 };

      req.headers.authorization = `Bearer ${mockToken}`;
      jwt.verify.mockReturnValue(mockDecoded);
      
      const { sql } = await import('../../../src/config/db.js');
      sql.mockResolvedValue([]); // No user found

      await protect(req, res, next);

      expect(next).toHaveBeenCalledWith(
        expect.any(AppError)
      );
      expect(next.mock.calls[0][0].message).toBe(
        'The user belonging to this token does no longer exist.'
      );
    });

    it('should call next with error if token is invalid', async () => {
      const mockToken = 'invalid-token';

      req.headers.authorization = `Bearer ${mockToken}`;
      jwt.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await protect(req, res, next);

      expect(next).toHaveBeenCalledWith(
        expect.any(AppError)
      );
      expect(next.mock.calls[0][0].message).toBe(
        'Invalid token. Please log in again!'
      );
    });
  });

  describe('restrictTo middleware', () => {
    it('should allow access for authorized roles', () => {
      req.user = { role: 'admin' };
      const middleware = restrictTo('admin', 'seller');

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should deny access for unauthorized roles', () => {
      req.user = { role: 'user' };
      const middleware = restrictTo('admin', 'seller');

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(
        expect.any(AppError)
      );
      expect(next.mock.calls[0][0].message).toBe(
        'You do not have permission to perform this action'
      );
      expect(next.mock.calls[0][0].statusCode).toBe(403);
    });
  });
});
