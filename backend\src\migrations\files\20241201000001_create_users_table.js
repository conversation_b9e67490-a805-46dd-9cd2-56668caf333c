// Migration: Create users table with indexes
// Created: 2024-12-01T00:00:01.000Z

export const up = async (sql) => {
  // Create users table
  await sql`
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
      email VARCHAR(255) UNIQUE NOT NULL,
      username VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
      password VARCHAR(255) NOT NULL,
      role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin', 'seller')),
      status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
      email_verified BOOLEAN DEFAULT FALSE,
      phone VARCHAR(20),
      avatar_url TEXT,
      last_login TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;

  // Create indexes for performance
  await sql`CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)`;

  // Create trigger for updated_at
  await sql`
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql'
  `;

  await sql`
    CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
  `;
};

export const down = async (sql) => {
  await sql`DROP TRIGGER IF EXISTS update_users_updated_at ON users`;
  await sql`DROP FUNCTION IF EXISTS update_updated_at_column()`;
  await sql`DROP TABLE IF EXISTS users CASCADE`;
};
