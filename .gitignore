# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
*.db
*.sqlite
*.sqlite3
database.db

# Logs
logs/
*.log

# Expo
.expo/
dist/
web-build/

# React Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
*.pem
Thumbs.db

# Build outputs
build/
dist/
out/

# Cache
.cache/
.parcel-cache/
.eslintcache

# TypeScript
*.tsbuildinfo

# Temporary files
tmp/
temp/
