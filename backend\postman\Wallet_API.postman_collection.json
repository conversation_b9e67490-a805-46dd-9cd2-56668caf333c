{"info": {"name": "Wallet API", "description": "Complete API collection for the Wallet application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5001/api", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}}, {"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"username\": \"johndo<PERSON>\",\n  \"password\": \"password123\",\n  \"role\": \"user\"\n}"}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('jwt_token', response.token);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"emailOrUsername\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/users/login", "host": ["{{base_url}}"], "path": ["users", "login"]}}}]}, {"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products", "host": ["{{base_url}}"], "path": ["products"]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/1", "host": ["{{base_url}}"], "path": ["products", "1"]}}}, {"name": "Create Product", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Product\",\n  \"description\": \"This is a test product description\",\n  \"price\": 99.99,\n  \"original_price\": 129.99,\n  \"discount_percentage\": 23,\n  \"images\": [\"https://example.com/image.jpg\"],\n  \"category\": \"electronics\",\n  \"stock\": 10,\n  \"featured\": true,\n  \"seller_id\": \"seller123\"\n}"}, "url": {"raw": "{{base_url}}/products", "host": ["{{base_url}}"], "path": ["products"]}}}, {"name": "Update Product", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product\",\n  \"description\": \"Updated description\",\n  \"price\": 149.99\n}"}, "url": {"raw": "{{base_url}}/products/1", "host": ["{{base_url}}"], "path": ["products", "1"]}}}, {"name": "Delete Product", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/products/1", "host": ["{{base_url}}"], "path": ["products", "1"]}}}]}, {"name": "Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "Create Category", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Electronics\"\n}"}, "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}]}, {"name": "Transactions", "item": [{"name": "Get User Transactions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/transactions/user123", "host": ["{{base_url}}"], "path": ["transactions", "user123"]}}}, {"name": "Create Transaction", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"user123\",\n  \"title\": \"Test Transaction\",\n  \"amount\": 100.50,\n  \"category\": \"income\"\n}"}, "url": {"raw": "{{base_url}}/transactions", "host": ["{{base_url}}"], "path": ["transactions"]}}}]}]}