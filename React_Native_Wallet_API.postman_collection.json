{"info": {"name": "React Native Wallet API", "description": "Complete API collection for React Native Wallet project", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5001/api", "type": "string"}], "item": [{"name": "Categories", "item": [{"name": "GET All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "GET Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/categories/20", "host": ["{{base_url}}"], "path": ["categories", "20"]}}}, {"name": "POST Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/categories", "host": ["{{base_url}}"], "path": ["categories"]}}}, {"name": "PUT Update Category", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/categories/30", "host": ["{{base_url}}"], "path": ["categories", "30"]}}}, {"name": "DELETE Category", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/categories/30", "host": ["{{base_url}}"], "path": ["categories", "30"]}}}]}, {"name": "Subcategories", "item": [{"name": "GET All Subcategories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/subcategories", "host": ["{{base_url}}"], "path": ["subcategories"]}}}, {"name": "GET Subcategories by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/subcategories/category/20", "host": ["{{base_url}}"], "path": ["subcategories", "category", "20"]}}}, {"name": "POST Create Subcategory", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Alt Kategori\",\n  \"category_id\": 20\n}"}, "url": {"raw": "{{base_url}}/subcategories", "host": ["{{base_url}}"], "path": ["subcategories"]}}}, {"name": "PUT Update Subcategory", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Güncellenmiş Alt Kategori\",\n  \"category_id\": 20\n}"}, "url": {"raw": "{{base_url}}/subcategories/1", "host": ["{{base_url}}"], "path": ["subcategories", "1"]}}}, {"name": "DELETE Subcategory", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/subcategories/1", "host": ["{{base_url}}"], "path": ["subcategories", "1"]}}}]}, {"name": "Products", "item": [{"name": "GET All Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products", "host": ["{{base_url}}"], "path": ["products"]}}}, {"name": "GET Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products/15", "host": ["{{base_url}}"], "path": ["products", "15"]}}}, {"name": "POST Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON> Ürün\",\n  \"description\": \"Test ürün açıklaması\",\n  \"price\": 1000,\n  \"original_price\": 1200,\n  \"discount_percentage\": 17,\n  \"images\": [\"https://example.com/test.jpg\"],\n  \"category\": \"Elektronik\",\n  \"stock\": 10,\n  \"featured\": true,\n  \"status\": \"active\",\n  \"seller_id\": \"test_user\",\n  \"specifications\": {\"color\": \"Siyah\", \"brand\": \"Test\"},\n  \"tags\": [\"test\", \"elektronik\"],\n  \"dynamic_pricing\": {\"enabled\": false}\n}"}, "url": {"raw": "{{base_url}}/products", "host": ["{{base_url}}"], "path": ["products"]}}}, {"name": "PUT Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Güncellenmiş Ürün\",\n  \"description\": \"Güncellenmiş açıklama\",\n  \"price\": 1100,\n  \"original_price\": 1300,\n  \"discount_percentage\": 15,\n  \"images\": [\"https://example.com/updated.jpg\"],\n  \"category\": \"Elektronik\",\n  \"stock\": 5,\n  \"featured\": false,\n  \"status\": \"active\",\n  \"specifications\": {\"color\": \"Beyaz\", \"brand\": \"Updated\"},\n  \"tags\": [\"updated\", \"elektronik\"],\n  \"dynamic_pricing\": {\"enabled\": true}\n}"}, "url": {"raw": "{{base_url}}/products/15", "host": ["{{base_url}}"], "path": ["products", "15"]}}}, {"name": "DELETE Product", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/products/15", "host": ["{{base_url}}"], "path": ["products", "15"]}}}]}, {"name": "Users", "item": [{"name": "GET All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "GET User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/3", "host": ["{{base_url}}"], "path": ["users", "3"]}}}, {"name": "POST Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON> Kullanıcı\",\n  \"email\": \"<EMAIL>\",\n  \"username\": \"testuser\",\n  \"password\": \"123456\",\n  \"role\": \"user\",\n  \"status\": \"active\",\n  \"approval_status\": \"approved\",\n  \"is_email_verified\": true,\n  \"can_bid\": false,\n  \"can_participate_in_lottery\": false,\n  \"profile\": {\"bio\": \"Test kullanıcı\"}\n}"}, "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}}}, {"name": "PUT Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>ü<PERSON>llenmiş Kullanıcı\",\n  \"email\": \"<EMAIL>\",\n  \"username\": \"updateduser\",\n  \"role\": \"moderator\",\n  \"status\": \"active\",\n  \"approval_status\": \"approved\",\n  \"is_email_verified\": true,\n  \"can_bid\": true,\n  \"can_participate_in_lottery\": true,\n  \"profile\": {\"bio\": \"Güncellenmiş profil\"}\n}"}, "url": {"raw": "{{base_url}}/users/8", "host": ["{{base_url}}"], "path": ["users", "8"]}}}, {"name": "DELETE User", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/users/8", "host": ["{{base_url}}"], "path": ["users", "8"]}}}]}]}