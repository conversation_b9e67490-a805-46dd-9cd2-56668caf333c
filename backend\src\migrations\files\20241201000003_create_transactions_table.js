// Migration: Create transactions table with indexes
// Created: 2024-12-01T00:00:03.000Z

export const up = async (sql) => {
  // Create transactions table
  await sql`
    CREATE TABLE IF NOT EXISTS transactions (
      id SERIAL PRIMARY KEY,
      user_id VARCHAR(255) NOT NULL,
      title VARCHAR(255) NOT NULL,
      amount DECIMAL(10,2) NOT NULL,
      category VARCHAR(100) NOT NULL,
      type VARCHAR(50) DEFAULT 'expense' CHECK (type IN ('income', 'expense', 'transfer')),
      description TEXT,
      reference_id VARCHAR(255),
      status VARCHAR(50) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
      payment_method VARCHAR(100),
      currency VARCHAR(3) DEFAULT 'USD',
      exchange_rate DECIMAL(10,4) DEFAULT 1.0,
      tags TEXT[],
      metadata JSONB DEFAULT '{}',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;

  // Create indexes for performance
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_category ON transactions(category)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_amount ON transactions(amount)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_reference_id ON transactions(reference_id)`;

  // Create composite indexes for common queries
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_user_date ON transactions(user_id, created_at)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_user_category ON transactions(user_id, category)`;
  await sql`CREATE INDEX IF NOT EXISTS idx_transactions_user_type ON transactions(user_id, type)`;

  // Create trigger for updated_at
  await sql`
    CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()
  `;
};

export const down = async (sql) => {
  await sql`DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions`;
  await sql`DROP TABLE IF EXISTS transactions CASCADE`;
};
