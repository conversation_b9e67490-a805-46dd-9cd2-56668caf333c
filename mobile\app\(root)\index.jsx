import { View, Text, FlatList } from 'react-native'
import React, { useEffect } from 'react'
import { ProductListItem } from '@/components/ProductListItem';
import { useBreakpointValue } from '@/components/ui/utils/use-break-point-value';
import { getProducts } from '@/hooks/useProducts';
import { useState } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';


export default function HomeScreen() {
  const { user } = useUser();
  const router = useRouter();

  const [products, setProducts] = useState([]);

    useEffect(() => {
        const fetchProducts = async () => {
            const data = await getProducts();
            setProducts(data);
        };
        fetchProducts();
    }, []);
    if (!products) {
        return <Text>Loading...</Text>;
    }
    if (products.length === 0) {
        return <Text>No products found</Text>;
    }
    console.log(products);

    const numColumns = useBreakpointValue({ 
            default: 2, 
            sm: 3 ,
            xl: 4
        }
    );

    return (
        <FlatList
            data={products}
            numColumns={numColumns}
            key={numColumns}
            contentContainerClassName='gap-2 max-w-[960px] mx-auto w-full'
            columnWrapperClassName='gap-2'
            renderItem={({ item }) =>
                <ProductListItem product={item} />}
        />

    );
}
