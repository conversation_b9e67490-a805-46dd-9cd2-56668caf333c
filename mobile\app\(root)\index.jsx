import { View, Text, FlatList } from 'react-native'
import React, { useEffect } from 'react'
import { ProductListItem } from '@/components/ProductListItem';
import { useBreakpointValue } from '@/components/ui/utils/use-break-point-value';
import { getProducts } from '@/hooks/useProducts';
import { useState } from 'react';
import { useUser } from '@clerk/clerk-expo';
import { useRouter } from 'expo-router';


export default function HomeScreen() {
  const { user } = useUser();
  const router = useRouter();

  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching products...');
        const data = await getProducts();
        console.log('Products received:', data);
        setProducts(data || []);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Ürünler yüklenirken hata oluştu');
        setProducts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Ürünler yükleniyor...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: 'red' }}>{error}</Text>
      </View>
    );
  }

  if (products.length === 0) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Henüz ürün bulunmuyor</Text>
      </View>
    );
  }

  const numColumns = useBreakpointValue({
    default: 2,
    sm: 3,
    xl: 4
  });

  return (
    <View style={{ flex: 1, padding: 10 }}>
      <FlatList
        data={products}
        numColumns={numColumns}
        key={numColumns}
        contentContainerClassName='gap-2 max-w-[960px] mx-auto w-full'
        columnWrapperClassName='gap-2'
        renderItem={({ item }) => (
          <ProductListItem product={item} />
        )}
        keyExtractor={(item) => item.id.toString()}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
}
