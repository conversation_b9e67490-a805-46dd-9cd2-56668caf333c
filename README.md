# 🏦 React Native Wallet - Complete E-commerce Solution

A comprehensive full-stack wallet and e-commerce application built with React Native (Expo) and Node.js, featuring modern development practices, comprehensive testing, and production-ready infrastructure.

## 🚀 **Features**

### **Backend API**

- ✅ **Authentication & Authorization**: JWT-based auth with RBAC
- ✅ **Product Management**: Full CRUD operations with categories
- ✅ **Transaction Management**: Financial transaction tracking
- ✅ **User Management**: Registration, login, profile management
- ✅ **Security**: Rate limiting, CORS, helmet security headers
- ✅ **Validation**: Input validation with <PERSON><PERSON> schemas
- ✅ **Error Handling**: Centralized error handling with logging
- ✅ **API Documentation**: Swagger/OpenAPI documentation
- ✅ **Performance Monitoring**: Real-time metrics and monitoring
- ✅ **Testing**: 35 tests (34 passing) - Unit & Integration

### **Mobile App**

- ✅ **Cross-platform**: iOS & Android with Expo
- ✅ **Authentication**: Clerk integration
- ✅ **State Management**: Zustand with persistence
- ✅ **Offline Support**: Offline-first architecture
- ✅ **Push Notifications**: Expo notifications
- ✅ **Deep Linking**: Universal links support
- ✅ **Product Browsing**: Categories, search, filters
- ✅ **Shopping Cart**: Add, remove, update quantities
- ✅ **Wishlist**: Save favorite products
- ✅ **User Profile**: Account management
- ✅ **Testing**: Component, Context, Hook tests
- ✅ **E2E Testing**: Detox integration

### **DevOps & Infrastructure**

- ✅ **CI/CD Pipelines**: GitHub Actions for both backend and mobile
- ✅ **Docker Support**: Multi-stage builds with security
- ✅ **Database Migrations**: Automated migration system
- ✅ **Database Seeding**: Sample data management
- ✅ **Database Backup**: Automated backup strategy
- ✅ **File Upload**: Secure file handling with validation
- ✅ **Caching Strategy**: Redis-based multi-layer caching
- ✅ **Query Optimization**: Performance monitoring and optimization
- ✅ **Monitoring Stack**: Prometheus, Grafana, Loki
- ✅ **Performance Tracking**: Real-time metrics and alerts
- ✅ **Security Scanning**: Automated vulnerability checks
- ✅ **CLI Tools**: Comprehensive management commands

## 📋 **Prerequisites**

- Node.js 18+
- PostgreSQL database
- Redis (for caching/rate limiting)
- Expo CLI (for mobile development)
- Docker & Docker Compose (for deployment)

## 🛠️ **Quick Start**

### **1. Clone Repository**

```bash
git clone <repository-url>
cd React_Native_Vallet
```

### **2. Backend Setup**

```bash
cd backend
npm install
cp .env.example .env
# Update .env with your database credentials
npm run dev
```

### **3. Mobile Setup**

```bash
cd mobile
npm install
cp .env.example .env
# Update .env with your Clerk keys
npm start
```

### **4. Docker Deployment**

```bash
# Copy environment files
cp .env.example .env
# Update with production values
docker-compose up -d
```

## 🧪 **Testing**

### **Backend Tests**

```bash
cd backend
npm test                 # All tests
npm run test:unit       # Unit tests only
npm run test:integration # Integration tests
npm run test:coverage   # With coverage report
```

### **Mobile Tests**

```bash
cd mobile
npm test                # Component tests
npm run test:watch     # Watch mode
npm run test:e2e       # E2E tests (requires simulator)
```

## 📊 **Monitoring & Metrics**

### **Available Dashboards**

- **API Documentation**: http://localhost:5001/api-docs
- **Prometheus Metrics**: http://localhost:5001/api/metrics
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Health Check**: http://localhost:5001/api/health

### **Key Metrics Tracked**

- API response times
- Request rates and error rates
- Memory and CPU usage
- Database performance
- Cache hit rates
- User activity

## 🏗️ **Architecture**

```
React_Native_Vallet/
├── backend/                 # Node.js API Server
│   ├── src/
│   │   ├── config/         # Database, logging, swagger
│   │   ├── middleware/     # Auth, validation, performance
│   │   ├── routes/         # API endpoints
│   │   └── server.js       # Main server file
│   ├── tests/              # Unit & integration tests
│   ├── logs/               # Application logs
│   └── postman/            # API collections
├── mobile/                 # React Native App
│   ├── app/                # Expo Router pages
│   ├── components/         # Reusable components
│   ├── contexts/           # React contexts
│   ├── tests/              # Component tests
│   └── e2e/                # End-to-end tests
├── monitoring/             # Prometheus, Grafana configs
├── .github/workflows/      # CI/CD pipelines
└── docker-compose.yml      # Production deployment
```

## 🔐 **Security Features**

- **JWT Authentication** with role-based access control
- **Rate Limiting** (Express + Upstash Redis)
- **Input Validation** with Joi schemas
- **Security Headers** with Helmet
- **CORS Protection** with configurable origins
- **SQL Injection Protection** with parameterized queries
- **Error Tracking** with Sentry integration

## 📈 **Performance Optimizations**

- **Response Time Monitoring** with alerts
- **Memory Usage Tracking** with leak detection
- **Database Query Optimization** with performance logs
- **Caching Strategy** with Redis
- **Graceful Shutdown** handling
- **Health Checks** for all services

## 🚀 **Deployment**

### **Development**

```bash
# Backend
cd backend && npm run dev

# Mobile
cd mobile && npm start
```

### **Production**

```bash
# Using Docker Compose
docker-compose up -d

# Manual deployment
cd backend && npm start
cd mobile && expo build
```

### **CI/CD Pipeline**

- **Automated Testing** on every push
- **Security Scanning** with Snyk
- **Code Coverage** reporting
- **Automated Deployment** to staging/production
- **E2E Testing** in CI environment

## 📊 **Test Coverage**

| Component         | Tests            | Coverage       |
| ----------------- | ---------------- | -------------- |
| Backend API       | 35 tests (34 ✅) | 85%+           |
| Mobile Components | 15+ tests        | 80%+           |
| E2E Scenarios     | 10+ flows        | Critical paths |
| Integration       | API endpoints    | All routes     |

## 🤝 **Contributing**

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Write tests for new features
4. Ensure all tests pass: `npm test`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push to branch: `git push origin feature/amazing-feature`
7. Open Pull Request

## 📄 **API Documentation**

- **Swagger UI**: http://localhost:5001/api-docs
- **Postman Collection**: `backend/postman/Wallet_API.postman_collection.json`
- **Health Check**: `GET /api/health`
- **Metrics**: `GET /api/metrics`

## 🔧 **Environment Variables**

### **Backend (.env)**

```env
DATABASE_URL=postgresql://...
JWT_SECRET=your-secret-key
UPSTASH_REDIS_REST_URL=...
UPSTASH_REDIS_REST_TOKEN=...
SENTRY_DSN=... (optional)
```

### **Mobile (.env)**

```env
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
EXPO_PUBLIC_API_URL=http://localhost:5001/api
EXPO_PUBLIC_NODE_ENV=development
```

## 📞 **Support**

- **Issues**: GitHub Issues
- **Documentation**: `/docs` folder
- **API Docs**: http://localhost:5001/api-docs
- **Monitoring**: http://localhost:3000 (Grafana)

## 📄 **License**

This project is licensed under the ISC License - see the LICENSE file for details.

---

**Built with ❤️ using modern development practices and production-ready infrastructure.**
#   w a l l e t 
 
 
