/* Web-specific styles for better UX */

/* Button hover effects */
.pressable-button:hover {
  opacity: 0.8;
  transform: scale(1.02);
  transition: all 0.2s ease;
}

.action-button:hover {
  opacity: 0.9;
  transform: scale(1.1);
  transition: all 0.2s ease;
}

.edit-button:hover {
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.delete-button:hover {
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.add-button:hover {
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transform: translateY(-1px);
}

/* Card hover effects */
.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

/* List item hover effects */
.list-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
  transition: background-color 0.2s ease;
}

/* Focus styles for accessibility */
.pressable-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition: opacity 0.2s ease, transform 0.2s ease;
}

/* Cursor styles */
.clickable {
  cursor: pointer;
}

.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .action-button:hover {
    transform: none;
  }
  
  .dashboard-card:hover {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .list-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .action-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
