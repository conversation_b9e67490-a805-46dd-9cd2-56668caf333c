import Joi from 'joi';
import { AppError } from './errorHandler.js';

// User validation schemas
const userRegistrationSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  username: Joi.string().min(3).max(30).required(),
  password: Joi.string().min(6).required(),
  role: Joi.string().valid('user', 'admin', 'seller').default('user'),
});

const userLoginSchema = Joi.object({
  emailOrUsername: Joi.string().required(),
  password: Joi.string().required(),
});

// Product validation schemas
const productSchema = Joi.object({
  name: Joi.string().min(2).max(100).required(),
  description: Joi.string().min(10).max(1000).required(),
  price: Joi.number().positive().required(),
  original_price: Joi.number().positive(),
  discount_percentage: Joi.number().min(0).max(100).default(0),
  images: Joi.array().items(Joi.string().uri()),
  category: Joi.string().required(),
  stock: Joi.number().integer().min(0).default(0),
  featured: Joi.boolean().default(false),
  status: Joi.string().valid('active', 'inactive', 'draft').default('active'),
  seller_id: Joi.string().required(),
  specifications: Joi.object().default({}),
  tags: Joi.array().items(Joi.string()).default([]),
  dynamic_pricing: Joi.object().default({})
});

// Category validation schemas
const categorySchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
});

// Transaction validation schemas
const transactionSchema = Joi.object({
  user_id: Joi.string().required(),
  title: Joi.string().min(2).max(100).required(),
  amount: Joi.number().required(),
  category: Joi.string().required(),
});

// Generic validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      return next(new AppError(errorMessage, 400));
    }

    req.body = value;
    next();
  };
};

// Specific validation middlewares
const validateUserRegistration = validate(userRegistrationSchema);
const validateUserLogin = validate(userLoginSchema);
const validateProduct = validate(productSchema);
const validateCategory = validate(categorySchema);
const validateTransaction = validate(transactionSchema);

export {
  validate,
  validateUserRegistration,
  validateUserLogin,
  validateProduct,
  validateCategory,
  validateTransaction,
  userRegistrationSchema,
  userLoginSchema,
  productSchema,
  categorySchema,
  transactionSchema
};
