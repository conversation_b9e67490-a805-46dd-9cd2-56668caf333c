import React from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { styled } from 'nativewind';

const StyledView = styled(View);
const StyledText = styled(Text);

export const LoadingSpinner = ({ 
  size = 'large', 
  color = '#007AFF', 
  text = 'Loading...', 
  fullScreen = false,
  overlay = false 
}) => {
  const containerClass = fullScreen 
    ? 'flex-1 justify-center items-center bg-white'
    : 'justify-center items-center p-4';

  const overlayClass = overlay 
    ? 'absolute inset-0 bg-black/50 justify-center items-center z-50'
    : '';

  return (
    <StyledView className={`${containerClass} ${overlayClass}`}>
      <ActivityIndicator size={size} color={color} />
      {text && (
        <StyledText className="mt-2 text-gray-600 text-center">
          {text}
        </StyledText>
      )}
    </StyledView>
  );
};

export const LoadingButton = ({ 
  loading, 
  children, 
  onPress, 
  disabled,
  className = '',
  ...props 
}) => {
  return (
    <StyledView 
      className={`bg-blue-500 rounded-lg px-4 py-3 ${disabled || loading ? 'opacity-50' : ''} ${className}`}
      onTouchEnd={loading || disabled ? undefined : onPress}
      {...props}
    >
      {loading ? (
        <StyledView className="flex-row items-center justify-center">
          <ActivityIndicator size="small" color="white" />
          <StyledText className="text-white font-medium ml-2">
            Loading...
          </StyledText>
        </StyledView>
      ) : (
        children
      )}
    </StyledView>
  );
};

export const LoadingCard = ({ loading, children, className = '' }) => {
  if (loading) {
    return (
      <StyledView className={`bg-white rounded-lg p-4 shadow-sm ${className}`}>
        <StyledView className="animate-pulse">
          <StyledView className="h-4 bg-gray-200 rounded mb-2" />
          <StyledView className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
          <StyledView className="h-4 bg-gray-200 rounded w-1/2" />
        </StyledView>
      </StyledView>
    );
  }

  return children;
};

export const LoadingList = ({ loading, itemCount = 5, className = '' }) => {
  if (!loading) return null;

  return (
    <StyledView className={className}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <StyledView key={index} className="bg-white rounded-lg p-4 mb-2 shadow-sm">
          <StyledView className="animate-pulse">
            <StyledView className="flex-row items-center mb-3">
              <StyledView className="w-12 h-12 bg-gray-200 rounded-full" />
              <StyledView className="ml-3 flex-1">
                <StyledView className="h-4 bg-gray-200 rounded mb-2" />
                <StyledView className="h-3 bg-gray-200 rounded w-2/3" />
              </StyledView>
            </StyledView>
            <StyledView className="h-3 bg-gray-200 rounded mb-2" />
            <StyledView className="h-3 bg-gray-200 rounded w-4/5" />
          </StyledView>
        </StyledView>
      ))}
    </StyledView>
  );
};

export const LoadingImage = ({ 
  source, 
  style, 
  className = '',
  loadingClassName = '',
  ...props 
}) => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);

  return (
    <StyledView className={`relative ${className}`}>
      {loading && (
        <StyledView className={`absolute inset-0 justify-center items-center bg-gray-100 ${loadingClassName}`}>
          <ActivityIndicator size="small" color="#666" />
        </StyledView>
      )}
      {error ? (
        <StyledView className="absolute inset-0 justify-center items-center bg-gray-100">
          <StyledText className="text-gray-500 text-xs">Failed to load</StyledText>
        </StyledView>
      ) : (
        <Image
          source={source}
          style={style}
          onLoad={() => setLoading(false)}
          onError={() => {
            setLoading(false);
            setError(true);
          }}
          {...props}
        />
      )}
    </StyledView>
  );
};
