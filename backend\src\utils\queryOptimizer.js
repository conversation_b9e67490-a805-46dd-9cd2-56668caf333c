import { sql } from '../config/db.js';
import logger from '../config/logger.js';

class QueryOptimizer {
  constructor() {
    this.slowQueryThreshold = parseInt(process.env.SLOW_QUERY_THRESHOLD) || 1000; // 1 second
    this.queryStats = new Map();
  }

  // Wrap SQL queries with performance monitoring
  async executeQuery(query, params = [], queryName = 'unknown') {
    const startTime = Date.now();
    const queryId = this.generateQueryId(queryName, params);

    try {
      const result = await query;
      const duration = Date.now() - startTime;

      // Log performance metrics
      this.logQueryPerformance(queryId, queryName, duration, true);

      // Alert for slow queries
      if (duration > this.slowQueryThreshold) {
        logger.warn('Slow query detected', {
          queryName,
          duration: `${duration}ms`,
          threshold: `${this.slowQueryThreshold}ms`,
          queryId
        });
      }

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logQueryPerformance(queryId, queryName, duration, false);
      
      logger.error('Query execution failed', {
        queryName,
        duration: `${duration}ms`,
        error: error.message,
        queryId
      });
      
      throw error;
    }
  }

  generateQueryId(queryName, params) {
    return `${queryName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  logQueryPerformance(queryId, queryName, duration, success) {
    if (!this.queryStats.has(queryName)) {
      this.queryStats.set(queryName, {
        count: 0,
        totalTime: 0,
        avgTime: 0,
        minTime: Infinity,
        maxTime: 0,
        errors: 0,
        lastExecuted: null
      });
    }

    const stats = this.queryStats.get(queryName);
    stats.count++;
    stats.totalTime += duration;
    stats.avgTime = stats.totalTime / stats.count;
    stats.minTime = Math.min(stats.minTime, duration);
    stats.maxTime = Math.max(stats.maxTime, duration);
    stats.lastExecuted = new Date();

    if (!success) {
      stats.errors++;
    }

    // Log summary every 100 queries
    if (stats.count % 100 === 0) {
      logger.info('Query performance summary', {
        queryName,
        ...stats,
        avgTime: `${stats.avgTime.toFixed(2)}ms`,
        minTime: `${stats.minTime}ms`,
        maxTime: `${stats.maxTime}ms`,
        errorRate: `${((stats.errors / stats.count) * 100).toFixed(2)}%`
      });
    }
  }

  // Get query statistics
  getQueryStats() {
    const stats = {};
    for (const [queryName, data] of this.queryStats) {
      stats[queryName] = {
        ...data,
        avgTime: parseFloat(data.avgTime.toFixed(2)),
        errorRate: parseFloat(((data.errors / data.count) * 100).toFixed(2))
      };
    }
    return stats;
  }

  // Optimized query builders
  async getProductsOptimized(filters = {}) {
    const { category, featured, status = 'active', limit = 20, offset = 0, sortBy = 'created_at', sortOrder = 'DESC' } = filters;
    
    let whereConditions = [`status = ${sql.escape(status)}`];
    let params = [];

    if (category) {
      whereConditions.push(`category = $${params.length + 1}`);
      params.push(category);
    }

    if (featured !== undefined) {
      whereConditions.push(`featured = $${params.length + 1}`);
      params.push(featured);
    }

    const whereClause = whereConditions.join(' AND ');
    const validSortColumns = ['created_at', 'price', 'name', 'rating', 'sales_count'];
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const query = sql`
      SELECT 
        id, name, description, price, original_price, discount_percentage,
        images, category, stock, featured, status, seller_id, rating, review_count,
        created_at, updated_at
      FROM products 
      WHERE ${sql.unsafe(whereClause)}
      ORDER BY ${sql.unsafe(sortColumn)} ${sql.unsafe(order)}
      LIMIT ${limit} OFFSET ${offset}
    `;

    return this.executeQuery(query, params, 'getProductsOptimized');
  }

  async getProductByIdOptimized(productId) {
    const query = sql`
      SELECT 
        p.*,
        u.name as seller_name,
        u.email as seller_email
      FROM products p
      LEFT JOIN users u ON p.seller_id = u.id::text
      WHERE p.id = ${productId} AND p.status = 'active'
    `;

    return this.executeQuery(query, [productId], 'getProductByIdOptimized');
  }

  async getUserTransactionsOptimized(userId, filters = {}) {
    const { category, type, limit = 20, offset = 0, startDate, endDate } = filters;
    
    let whereConditions = [`user_id = $1`];
    let params = [userId];

    if (category) {
      whereConditions.push(`category = $${params.length + 1}`);
      params.push(category);
    }

    if (type) {
      whereConditions.push(`type = $${params.length + 1}`);
      params.push(type);
    }

    if (startDate) {
      whereConditions.push(`created_at >= $${params.length + 1}`);
      params.push(startDate);
    }

    if (endDate) {
      whereConditions.push(`created_at <= $${params.length + 1}`);
      params.push(endDate);
    }

    const whereClause = whereConditions.join(' AND ');

    const query = sql`
      SELECT 
        id, title, amount, category, type, description, status,
        payment_method, currency, created_at, updated_at
      FROM transactions 
      WHERE ${sql.unsafe(whereClause)}
      ORDER BY created_at DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    return this.executeQuery(query, params, 'getUserTransactionsOptimized');
  }

  async searchProductsOptimized(searchTerm, filters = {}) {
    const { category, minPrice, maxPrice, limit = 20, offset = 0 } = filters;
    
    let whereConditions = [`status = 'active'`];
    let params = [searchTerm];

    if (category) {
      whereConditions.push(`category = $${params.length + 1}`);
      params.push(category);
    }

    if (minPrice) {
      whereConditions.push(`price >= $${params.length + 1}`);
      params.push(minPrice);
    }

    if (maxPrice) {
      whereConditions.push(`price <= $${params.length + 1}`);
      params.push(maxPrice);
    }

    const whereClause = whereConditions.join(' AND ');

    const query = sql`
      SELECT 
        id, name, description, price, original_price, discount_percentage,
        images, category, stock, featured, rating, review_count,
        ts_rank(to_tsvector('english', name || ' ' || COALESCE(description, '')), plainto_tsquery('english', $1)) as rank
      FROM products 
      WHERE ${sql.unsafe(whereClause)}
        AND to_tsvector('english', name || ' ' || COALESCE(description, '')) @@ plainto_tsquery('english', $1)
      ORDER BY rank DESC, rating DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    return this.executeQuery(query, params, 'searchProductsOptimized');
  }

  // Database maintenance queries
  async analyzeTableStats() {
    const tables = ['users', 'products', 'transactions', 'categories'];
    const stats = {};

    for (const table of tables) {
      try {
        const query = sql`
          SELECT 
            schemaname,
            tablename,
            attname,
            n_distinct,
            correlation
          FROM pg_stats 
          WHERE tablename = ${table}
          ORDER BY n_distinct DESC
        `;

        const result = await this.executeQuery(query, [], `analyzeTable_${table}`);
        stats[table] = result;
      } catch (error) {
        logger.error(`Failed to analyze table ${table}:`, error);
        stats[table] = { error: error.message };
      }
    }

    return stats;
  }

  async getSlowQueries() {
    try {
      const query = sql`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows,
          100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
        FROM pg_stat_statements 
        WHERE mean_time > ${this.slowQueryThreshold}
        ORDER BY mean_time DESC 
        LIMIT 10
      `;

      return this.executeQuery(query, [], 'getSlowQueries');
    } catch (error) {
      logger.warn('pg_stat_statements extension not available');
      return [];
    }
  }

  async optimizeDatabase() {
    const optimizations = [];

    try {
      // Update table statistics
      await sql`ANALYZE`;
      optimizations.push('Table statistics updated');

      // Reindex if needed (be careful in production)
      if (process.env.NODE_ENV !== 'production') {
        await sql`REINDEX DATABASE ${sql.unsafe(process.env.DB_NAME || 'wallet_db')}`;
        optimizations.push('Database reindexed');
      }

      logger.info('Database optimization completed', { optimizations });
      return optimizations;
    } catch (error) {
      logger.error('Database optimization failed:', error);
      throw error;
    }
  }
}

// Create optimizer instance
const queryOptimizer = new QueryOptimizer();

export default queryOptimizer;
