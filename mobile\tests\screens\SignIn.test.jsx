import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import SignInScreen from '../../app/(auth)/sign-in';

// Mock expo-router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    replace: jest.fn(),
  }),
}));

// Mock Clerk
const mockSignIn = {
  create: jest.fn(),
};

const mockSetActive = jest.fn();

jest.mock('@clerk/clerk-expo', () => ({
  useSignIn: () => ({
    signIn: mockSignIn,
    setActive: mockSetActive,
    isLoaded: true,
  }),
}));

describe('SignIn Screen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders sign in form correctly', () => {
    const { getByPlaceholderText, getByText } = render(<SignInScreen />);
    
    expect(getByPlaceholderText('Enter email')).toBeTruthy();
    expect(getByPlaceholderText('Enter password')).toBeTruthy();
    expect(getByText('Sign In')).toBeTruthy();
  });

  it('handles successful sign in', async () => {
    const mockRouter = { replace: jest.fn() };
    
    mockSignIn.create.mockResolvedValueOnce({
      status: 'complete',
      createdSessionId: 'session123',
    });

    const { getByPlaceholderText, getByText } = render(<SignInScreen />);
    
    const emailInput = getByPlaceholderText('Enter email');
    const passwordInput = getByPlaceholderText('Enter password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(mockSignIn.create).toHaveBeenCalledWith({
        identifier: '<EMAIL>',
        password: 'password123',
      });
    });

    expect(mockSetActive).toHaveBeenCalledWith({
      session: 'session123',
    });
  });

  it('handles sign in error', async () => {
    mockSignIn.create.mockRejectedValueOnce(new Error('Invalid credentials'));

    const { getByPlaceholderText, getByText } = render(<SignInScreen />);
    
    const emailInput = getByPlaceholderText('Enter email');
    const passwordInput = getByPlaceholderText('Enter password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'wrongpassword');
    fireEvent.press(signInButton);

    await waitFor(() => {
      expect(mockSignIn.create).toHaveBeenCalled();
    });

    // Should show error message
    expect(getByText(/error/i)).toBeTruthy();
  });

  it('validates empty fields', () => {
    const { getByText } = render(<SignInScreen />);
    
    const signInButton = getByText('Sign In');
    fireEvent.press(signInButton);

    // Should not call sign in with empty fields
    expect(mockSignIn.create).not.toHaveBeenCalled();
  });

  it('shows loading state during sign in', async () => {
    mockSignIn.create.mockImplementationOnce(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );

    const { getByPlaceholderText, getByText } = render(<SignInScreen />);
    
    const emailInput = getByPlaceholderText('Enter email');
    const passwordInput = getByPlaceholderText('Enter password');
    const signInButton = getByText('Sign In');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(signInButton);

    // Should show loading state
    expect(getByText(/signing in/i)).toBeTruthy();
  });

  it('navigates to sign up screen', () => {
    const { getByText } = render(<SignInScreen />);
    
    const signUpLink = getByText(/sign up/i);
    fireEvent.press(signUpLink);

    // Should navigate to sign up (tested via router mock)
  });
});
