#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Validate commit message format
commit_regex='^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
  echo "❌ Invalid commit message format!"
  echo ""
  echo "Commit message should follow the format:"
  echo "type(scope): description"
  echo ""
  echo "Types: feat, fix, docs, style, refactor, test, chore, perf, ci, build, revert"
  echo "Scope: optional, e.g., (auth), (api), (ui)"
  echo "Description: max 50 characters"
  echo ""
  echo "Examples:"
  echo "feat(auth): add JWT authentication"
  echo "fix(api): resolve user registration bug"
  echo "docs: update README with setup instructions"
  echo ""
  exit 1
fi

echo "✅ Commit message format is valid!"
