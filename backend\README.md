# Wallet Backend API

A comprehensive backend API for a wallet and e-commerce application built with Node.js, Express, and PostgreSQL.

## 🚀 Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Product Management**: CRUD operations for products with categories
- **Transaction Management**: Financial transaction tracking
- **User Management**: User registration, login, and profile management
- **Security**: Rate limiting, CORS, helmet security headers
- **Validation**: Input validation with Jo<PERSON>
- **Error Handling**: Centralized error handling with logging
- **API Documentation**: Swagger/OpenAPI documentation
- **Testing**: Unit and integration tests with Jest
- **Logging**: Structured logging with Winston

## 📋 Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- Redis (for rate limiting)

## 🛠️ Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Update the `.env` file with your configuration:
   ```env
   DATABASE_URL=your-postgresql-connection-string
   JWT_SECRET=your-jwt-secret
   UPSTASH_REDIS_REST_URL=your-redis-url
   UPSTASH_REDIS_REST_TOKEN=your-redis-token
   ```

## 🏃‍♂️ Running the Application

### Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Testing
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration
```

## 📚 API Documentation

Once the server is running, you can access:

- **Swagger UI**: http://localhost:5001/api-docs
- **Health Check**: http://localhost:5001/api/health
- **Postman Collection**: Import `postman/Wallet_API.postman_collection.json`

## 🏗️ Project Structure

```
backend/
├── src/
│   ├── config/          # Configuration files
│   │   ├── db.js        # Database configuration
│   │   ├── logger.js    # Winston logger setup
│   │   └── swagger.js   # Swagger configuration
│   ├── middleware/      # Express middlewares
│   │   ├── auth.js      # Authentication middleware
│   │   ├── errorHandler.js # Error handling
│   │   └── validation.js # Input validation
│   ├── routes/          # API routes
│   │   ├── productRoutes.js
│   │   ├── userRoutes.js
│   │   └── ...
│   └── server.js        # Main server file
├── tests/               # Test files
│   ├── unit/           # Unit tests
│   ├── integration/    # Integration tests
│   └── setup.js        # Test setup
├── logs/               # Log files
├── postman/            # Postman collections
└── package.json
```

## 🔐 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | Yes |
| `JWT_SECRET` | Secret for JWT token signing | Yes |
| `JWT_EXPIRES_IN` | JWT token expiration time | No (default: 24h) |
| `PORT` | Server port | No (default: 5001) |
| `NODE_ENV` | Environment (development/production) | No |
| `UPSTASH_REDIS_REST_URL` | Redis URL for rate limiting | Yes |
| `UPSTASH_REDIS_REST_TOKEN` | Redis token | Yes |
| `ALLOWED_ORIGINS` | CORS allowed origins | No |
| `SENTRY_DSN` | Sentry DSN for error tracking | No |

## 🧪 Testing

The project includes comprehensive testing:

- **Unit Tests**: Test individual functions and middleware
- **Integration Tests**: Test API endpoints
- **Test Coverage**: Aim for >80% coverage

Test files are located in the `tests/` directory and follow the naming convention `*.test.js`.

## 📊 Logging

Logs are written to:
- Console (development)
- `logs/combined.log` (all logs)
- `logs/error.log` (error logs only)

Log levels: error, warn, info, debug

## 🔒 Security Features

- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Rate Limiting**: Request rate limiting
- **JWT Authentication**: Secure token-based auth
- **Input Validation**: Joi schema validation
- **SQL Injection Protection**: Parameterized queries

## 🚀 Deployment

1. Set `NODE_ENV=production`
2. Update environment variables for production
3. Run database migrations
4. Start the application with `npm start`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new features
4. Ensure all tests pass
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.
