import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 5001;

app.use(cors());
app.use(express.json());

app.get('/', (req, res) => {
    res.json({ message: 'Server is working!' });
});

app.get('/api/categories', (req, res) => {
    res.json([
        { id: 1, name: 'Test Category', created_at: new Date() }
    ]);
});

app.get('/api/products', (req, res) => {
    res.json([
        { id: 1, name: 'Test Product', price: 100, created_at: new Date() }
    ]);
});

app.listen(PORT, () => {
    console.log(`Test server running on port ${PORT}`);
});
