import fs from 'fs';
import path from 'path';
import { sql } from '../config/db.js';
import logger from '../config/logger.js';

class MigrationRunner {
  constructor() {
    this.migrationsPath = path.join(process.cwd(), 'src/migrations/files');
    this.migrationTable = 'schema_migrations';
  }

  async init() {
    // Create migrations table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS ${sql(this.migrationTable)} (
        id SERIAL PRIMARY KEY,
        version VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    logger.info('Migration table initialized');
  }

  async getExecutedMigrations() {
    const result = await sql`
      SELECT version FROM ${sql(this.migrationTable)} 
      ORDER BY version ASC
    `;
    return result.map(row => row.version);
  }

  async getPendingMigrations() {
    const executedMigrations = await this.getExecutedMigrations();
    const allMigrations = this.getAllMigrationFiles();
    
    return allMigrations.filter(migration => 
      !executedMigrations.includes(migration.version)
    );
  }

  getAllMigrationFiles() {
    if (!fs.existsSync(this.migrationsPath)) {
      fs.mkdirSync(this.migrationsPath, { recursive: true });
      return [];
    }

    const files = fs.readdirSync(this.migrationsPath)
      .filter(file => file.endsWith('.js'))
      .sort();

    return files.map(file => {
      const version = file.split('_')[0];
      const name = file.replace('.js', '').substring(version.length + 1);
      return { version, name, filename: file };
    });
  }

  async runMigrations() {
    await this.init();
    const pendingMigrations = await this.getPendingMigrations();

    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations');
      return;
    }

    logger.info(`Running ${pendingMigrations.length} pending migrations`);

    for (const migration of pendingMigrations) {
      await this.runSingleMigration(migration);
    }

    logger.info('All migrations completed successfully');
  }

  async runSingleMigration(migration) {
    const migrationPath = path.join(this.migrationsPath, migration.filename);
    
    try {
      logger.info(`Running migration: ${migration.version} - ${migration.name}`);
      
      const migrationModule = await import(migrationPath);
      const { up } = migrationModule.default || migrationModule;

      if (typeof up !== 'function') {
        throw new Error(`Migration ${migration.filename} does not export an 'up' function`);
      }

      // Run the migration
      await up(sql);

      // Record the migration as executed
      await sql`
        INSERT INTO ${sql(this.migrationTable)} (version, name)
        VALUES (${migration.version}, ${migration.name})
      `;

      logger.info(`Migration completed: ${migration.version} - ${migration.name}`);
    } catch (error) {
      logger.error(`Migration failed: ${migration.version} - ${migration.name}`, error);
      throw error;
    }
  }

  async rollback(steps = 1) {
    const executedMigrations = await sql`
      SELECT version, name FROM ${sql(this.migrationTable)} 
      ORDER BY version DESC 
      LIMIT ${steps}
    `;

    if (executedMigrations.length === 0) {
      logger.info('No migrations to rollback');
      return;
    }

    for (const migration of executedMigrations) {
      await this.rollbackSingleMigration(migration);
    }
  }

  async rollbackSingleMigration(migration) {
    const migrationPath = path.join(this.migrationsPath, `${migration.version}_${migration.name}.js`);
    
    try {
      logger.info(`Rolling back migration: ${migration.version} - ${migration.name}`);
      
      const migrationModule = await import(migrationPath);
      const { down } = migrationModule.default || migrationModule;

      if (typeof down !== 'function') {
        throw new Error(`Migration ${migration.version}_${migration.name}.js does not export a 'down' function`);
      }

      // Run the rollback
      await down(sql);

      // Remove the migration record
      await sql`
        DELETE FROM ${sql(this.migrationTable)} 
        WHERE version = ${migration.version}
      `;

      logger.info(`Migration rolled back: ${migration.version} - ${migration.name}`);
    } catch (error) {
      logger.error(`Migration rollback failed: ${migration.version} - ${migration.name}`, error);
      throw error;
    }
  }

  async createMigration(name) {
    const timestamp = new Date().toISOString().replace(/[-:T]/g, '').split('.')[0];
    const filename = `${timestamp}_${name.replace(/\s+/g, '_').toLowerCase()}.js`;
    const filepath = path.join(this.migrationsPath, filename);

    const template = `// Migration: ${name}
// Created: ${new Date().toISOString()}

export const up = async (sql) => {
  // Add your migration logic here
  // Example:
  // await sql\`
  //   CREATE TABLE example (
  //     id SERIAL PRIMARY KEY,
  //     name VARCHAR(255) NOT NULL,
  //     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  //   )
  // \`;
};

export const down = async (sql) => {
  // Add your rollback logic here
  // Example:
  // await sql\`DROP TABLE IF EXISTS example\`;
};
`;

    if (!fs.existsSync(this.migrationsPath)) {
      fs.mkdirSync(this.migrationsPath, { recursive: true });
    }

    fs.writeFileSync(filepath, template);
    logger.info(`Migration created: ${filename}`);
    return filename;
  }
}

export default MigrationRunner;
