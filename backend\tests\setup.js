import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Global test setup
beforeAll(async () => {
  // Setup test database or mock services
  console.log('Setting up test environment...');
});

afterAll(async () => {
  // Cleanup after all tests
  console.log('Cleaning up test environment...');
});

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    username: 'testuser',
    role: 'user',
    status: 'active'
  }),
  
  createMockProduct: () => ({
    id: 1,
    name: 'Test Product',
    description: 'Test Description',
    price: 99.99,
    category: 'electronics',
    stock: 10,
    seller_id: 'user123'
  })
};
