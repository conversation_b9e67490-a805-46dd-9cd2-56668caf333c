import 'react-native-gesture-handler/jestSetup';

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock expo modules
jest.mock('expo-constants', () => ({
  default: {
    expoConfig: {
      extra: {
        clerkPublishableKey: 'test-key'
      }
    }
  }
}));

jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useLocalSearchParams: () => ({}),
  Link: ({ children }) => children,
  Stack: ({ children }) => children,
  Slot: ({ children }) => children,
}));

jest.mock('@clerk/clerk-expo', () => ({
  useUser: () => ({
    isSignedIn: true,
    isLoaded: true,
    user: {
      id: 'test-user-id',
      emailAddresses: [{ emailAddress: '<EMAIL>' }],
      firstName: 'Test',
      lastName: 'User'
    }
  }),
  useSignIn: () => ({
    signIn: {
      create: jest.fn(),
    },
    setActive: jest.fn(),
    isLoaded: true
  }),
  useSignUp: () => ({
    signUp: {
      create: jest.fn(),
      prepareEmailAddressVerification: jest.fn(),
      attemptEmailAddressVerification: jest.fn(),
    },
    setActive: jest.fn(),
    isLoaded: true
  }),
  ClerkProvider: ({ children }) => children,
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock fetch
global.fetch = jest.fn();

// Setup global test utilities
global.testUtils = {
  mockUser: {
    id: 'test-user-id',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
    firstName: 'Test',
    lastName: 'User'
  },
  
  mockProduct: {
    id: 1,
    name: 'Test Product',
    description: 'Test Description',
    price: 99.99,
    image: 'https://example.com/image.jpg'
  },
  
  mockApiResponse: (data, status = 200) => {
    global.fetch.mockResolvedValueOnce({
      ok: status >= 200 && status < 300,
      status,
      json: async () => data,
    });
  },
  
  mockApiError: (message = 'API Error', status = 500) => {
    global.fetch.mockRejectedValueOnce(new Error(message));
  }
};

// Silence console warnings during tests
console.warn = jest.fn();
console.error = jest.fn();
