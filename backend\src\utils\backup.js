import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';
import cron from 'node-cron';
import logger from '../config/logger.js';

const execAsync = promisify(exec);

class DatabaseBackup {
  constructor() {
    this.backupDir = path.join(process.cwd(), 'backups');
    this.maxBackups = parseInt(process.env.MAX_BACKUPS) || 30;
    this.dbUrl = process.env.DATABASE_URL;
    
    // Ensure backup directory exists
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  async createBackup(type = 'manual') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `backup_${type}_${timestamp}.sql`;
      const filepath = path.join(this.backupDir, filename);

      logger.info(`Creating ${type} backup: ${filename}`);

      // Create PostgreSQL dump
      const command = `pg_dump "${this.dbUrl}" > "${filepath}"`;
      await execAsync(command);

      // Compress the backup
      const compressedFile = `${filepath}.gz`;
      await execAsync(`gzip "${filepath}"`);

      const stats = fs.statSync(compressedFile);
      const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);

      logger.info(`Backup created successfully: ${filename}.gz (${fileSizeMB} MB)`);

      // Clean old backups
      await this.cleanOldBackups();

      return {
        filename: `${filename}.gz`,
        filepath: compressedFile,
        size: stats.size,
        type
      };
    } catch (error) {
      logger.error('Backup creation failed:', error);
      throw error;
    }
  }

  async restoreBackup(backupFile) {
    try {
      const filepath = path.join(this.backupDir, backupFile);
      
      if (!fs.existsSync(filepath)) {
        throw new Error(`Backup file not found: ${backupFile}`);
      }

      logger.info(`Restoring backup: ${backupFile}`);

      // Decompress if needed
      let sqlFile = filepath;
      if (filepath.endsWith('.gz')) {
        sqlFile = filepath.replace('.gz', '');
        await execAsync(`gunzip -c "${filepath}" > "${sqlFile}"`);
      }

      // Restore database
      const command = `psql "${this.dbUrl}" < "${sqlFile}"`;
      await execAsync(command);

      // Clean up decompressed file if it was created
      if (filepath.endsWith('.gz') && fs.existsSync(sqlFile)) {
        fs.unlinkSync(sqlFile);
      }

      logger.info(`Backup restored successfully: ${backupFile}`);
    } catch (error) {
      logger.error('Backup restoration failed:', error);
      throw error;
    }
  }

  async listBackups() {
    try {
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.startsWith('backup_') && file.endsWith('.sql.gz'))
        .map(file => {
          const filepath = path.join(this.backupDir, file);
          const stats = fs.statSync(filepath);
          return {
            filename: file,
            size: stats.size,
            created: stats.birthtime,
            type: file.includes('_auto_') ? 'automatic' : 'manual'
          };
        })
        .sort((a, b) => b.created - a.created);

      return files;
    } catch (error) {
      logger.error('Failed to list backups:', error);
      throw error;
    }
  }

  async cleanOldBackups() {
    try {
      const backups = await this.listBackups();
      
      if (backups.length > this.maxBackups) {
        const backupsToDelete = backups.slice(this.maxBackups);
        
        for (const backup of backupsToDelete) {
          const filepath = path.join(this.backupDir, backup.filename);
          fs.unlinkSync(filepath);
          logger.info(`Deleted old backup: ${backup.filename}`);
        }
      }
    } catch (error) {
      logger.error('Failed to clean old backups:', error);
    }
  }

  async deleteBackup(filename) {
    try {
      const filepath = path.join(this.backupDir, filename);
      
      if (!fs.existsSync(filepath)) {
        throw new Error(`Backup file not found: ${filename}`);
      }

      fs.unlinkSync(filepath);
      logger.info(`Backup deleted: ${filename}`);
    } catch (error) {
      logger.error('Failed to delete backup:', error);
      throw error;
    }
  }

  scheduleAutomaticBackups() {
    // Daily backup at 2 AM
    cron.schedule('0 2 * * *', async () => {
      try {
        await this.createBackup('auto');
        logger.info('Automatic daily backup completed');
      } catch (error) {
        logger.error('Automatic backup failed:', error);
      }
    });

    // Weekly backup on Sunday at 3 AM
    cron.schedule('0 3 * * 0', async () => {
      try {
        await this.createBackup('weekly');
        logger.info('Automatic weekly backup completed');
      } catch (error) {
        logger.error('Automatic weekly backup failed:', error);
      }
    });

    logger.info('Automatic backup schedules initialized');
  }

  async getBackupStats() {
    try {
      const backups = await this.listBackups();
      const totalSize = backups.reduce((sum, backup) => sum + backup.size, 0);
      const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);

      return {
        totalBackups: backups.length,
        totalSize: totalSize,
        totalSizeMB: totalSizeMB,
        oldestBackup: backups.length > 0 ? backups[backups.length - 1].created : null,
        newestBackup: backups.length > 0 ? backups[0].created : null,
        automaticBackups: backups.filter(b => b.type === 'automatic').length,
        manualBackups: backups.filter(b => b.type === 'manual').length
      };
    } catch (error) {
      logger.error('Failed to get backup stats:', error);
      throw error;
    }
  }
}

export default DatabaseBackup;
