import Stripe from 'stripe';
import { AppError } from '../middleware/errorHandler.js';
import logger from '../config/logger.js';
import { sql } from '../config/db.js';

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

class PaymentService {
  constructor() {
    this.stripe = stripe;
  }

  // Create payment intent
  async createPaymentIntent(amount, currency = 'usd', metadata = {}) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      logger.info('Payment intent created:', {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
      });

      return paymentIntent;
    } catch (error) {
      logger.error('Failed to create payment intent:', error);
      throw new AppError('Failed to create payment intent', 500);
    }
  }

  // Confirm payment intent
  async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, {
        payment_method: paymentMethodId,
      });

      logger.info('Payment intent confirmed:', {
        id: paymentIntent.id,
        status: paymentIntent.status,
      });

      return paymentIntent;
    } catch (error) {
      logger.error('Failed to confirm payment intent:', error);
      throw new AppError('Payment confirmation failed', 400);
    }
  }

  // Create customer
  async createCustomer(email, name, metadata = {}) {
    try {
      const customer = await this.stripe.customers.create({
        email,
        name,
        metadata,
      });

      logger.info('Customer created:', {
        id: customer.id,
        email: customer.email,
      });

      return customer;
    } catch (error) {
      logger.error('Failed to create customer:', error);
      throw new AppError('Failed to create customer', 500);
    }
  }

  // Get customer
  async getCustomer(customerId) {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      return customer;
    } catch (error) {
      logger.error('Failed to get customer:', error);
      throw new AppError('Customer not found', 404);
    }
  }

  // Create subscription
  async createSubscription(customerId, priceId, metadata = {}) {
    try {
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        metadata,
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
      });

      logger.info('Subscription created:', {
        id: subscription.id,
        customer: subscription.customer,
        status: subscription.status,
      });

      return subscription;
    } catch (error) {
      logger.error('Failed to create subscription:', error);
      throw new AppError('Failed to create subscription', 500);
    }
  }

  // Process refund
  async processRefund(paymentIntentId, amount = null, reason = 'requested_by_customer') {
    try {
      const refundData = {
        payment_intent: paymentIntentId,
        reason,
      };

      if (amount) {
        refundData.amount = Math.round(amount * 100); // Convert to cents
      }

      const refund = await this.stripe.refunds.create(refundData);

      logger.info('Refund processed:', {
        id: refund.id,
        amount: refund.amount,
        status: refund.status,
      });

      return refund;
    } catch (error) {
      logger.error('Failed to process refund:', error);
      throw new AppError('Refund processing failed', 500);
    }
  }

  // Handle webhook events
  async handleWebhook(payload, signature) {
    try {
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );

      logger.info('Webhook received:', {
        type: event.type,
        id: event.id,
      });

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        case 'customer.subscription.created':
          await this.handleSubscriptionCreated(event.data.object);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object);
          break;
        default:
          logger.info(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      logger.error('Webhook handling failed:', error);
      throw new AppError('Webhook handling failed', 400);
    }
  }

  // Handle successful payment
  async handlePaymentSucceeded(paymentIntent) {
    try {
      // Update order status in database
      await sql`
        UPDATE orders 
        SET 
          status = 'paid',
          payment_intent_id = ${paymentIntent.id},
          paid_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE payment_intent_id = ${paymentIntent.id}
      `;

      logger.info('Payment succeeded, order updated:', {
        paymentIntentId: paymentIntent.id,
        amount: paymentIntent.amount,
      });

      // Send confirmation email (implement email service)
      // await this.sendPaymentConfirmationEmail(paymentIntent);
    } catch (error) {
      logger.error('Failed to handle payment success:', error);
    }
  }

  // Handle failed payment
  async handlePaymentFailed(paymentIntent) {
    try {
      // Update order status in database
      await sql`
        UPDATE orders 
        SET 
          status = 'payment_failed',
          payment_intent_id = ${paymentIntent.id},
          updated_at = CURRENT_TIMESTAMP
        WHERE payment_intent_id = ${paymentIntent.id}
      `;

      logger.info('Payment failed, order updated:', {
        paymentIntentId: paymentIntent.id,
        lastPaymentError: paymentIntent.last_payment_error,
      });

      // Send failure notification email
      // await this.sendPaymentFailureEmail(paymentIntent);
    } catch (error) {
      logger.error('Failed to handle payment failure:', error);
    }
  }

  // Handle subscription events
  async handleSubscriptionCreated(subscription) {
    try {
      await sql`
        INSERT INTO subscriptions (
          stripe_subscription_id,
          customer_id,
          status,
          current_period_start,
          current_period_end,
          created_at
        ) VALUES (
          ${subscription.id},
          ${subscription.customer},
          ${subscription.status},
          ${new Date(subscription.current_period_start * 1000)},
          ${new Date(subscription.current_period_end * 1000)},
          CURRENT_TIMESTAMP
        )
      `;

      logger.info('Subscription created in database:', {
        subscriptionId: subscription.id,
        customerId: subscription.customer,
      });
    } catch (error) {
      logger.error('Failed to handle subscription creation:', error);
    }
  }

  async handleSubscriptionUpdated(subscription) {
    try {
      await sql`
        UPDATE subscriptions 
        SET 
          status = ${subscription.status},
          current_period_start = ${new Date(subscription.current_period_start * 1000)},
          current_period_end = ${new Date(subscription.current_period_end * 1000)},
          updated_at = CURRENT_TIMESTAMP
        WHERE stripe_subscription_id = ${subscription.id}
      `;

      logger.info('Subscription updated in database:', {
        subscriptionId: subscription.id,
        status: subscription.status,
      });
    } catch (error) {
      logger.error('Failed to handle subscription update:', error);
    }
  }

  async handleSubscriptionDeleted(subscription) {
    try {
      await sql`
        UPDATE subscriptions 
        SET 
          status = 'cancelled',
          cancelled_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
        WHERE stripe_subscription_id = ${subscription.id}
      `;

      logger.info('Subscription cancelled in database:', {
        subscriptionId: subscription.id,
      });
    } catch (error) {
      logger.error('Failed to handle subscription deletion:', error);
    }
  }

  async handleInvoicePaymentSucceeded(invoice) {
    logger.info('Invoice payment succeeded:', {
      invoiceId: invoice.id,
      subscriptionId: invoice.subscription,
      amount: invoice.amount_paid,
    });
  }

  async handleInvoicePaymentFailed(invoice) {
    logger.info('Invoice payment failed:', {
      invoiceId: invoice.id,
      subscriptionId: invoice.subscription,
      amount: invoice.amount_due,
    });
  }

  // Get payment methods for customer
  async getPaymentMethods(customerId) {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });

      return paymentMethods.data;
    } catch (error) {
      logger.error('Failed to get payment methods:', error);
      throw new AppError('Failed to get payment methods', 500);
    }
  }

  // Detach payment method
  async detachPaymentMethod(paymentMethodId) {
    try {
      const paymentMethod = await this.stripe.paymentMethods.detach(paymentMethodId);
      return paymentMethod;
    } catch (error) {
      logger.error('Failed to detach payment method:', error);
      throw new AppError('Failed to remove payment method', 500);
    }
  }
}

export default new PaymentService();
